<?php

namespace App\Console\Commands;

use App\Services\EventService;
use Illuminate\Console\Command;

class TriggerChargeSuccess extends Command
{
    protected $signature = 'payment:trigger-success {reference} {amount} {channel}';
    protected $description = 'Trigger charge success for testing';

    public function handle()
    {
        $payload = [
            'data' => [
                'reference' => $this->argument('reference'),
                'amount' => (int) $this->argument('amount'),
                'channel' => $this->argument('channel')
            ]
        ];

        $service = app(EventService::class);
        $service->handleChargeSuccess($payload);

        $this->info('Charge success event triggered successfully!');
    }
}

// Run with: php artisan payment:trigger-success REF20250711080929-6870C6B9D1166 300000 card