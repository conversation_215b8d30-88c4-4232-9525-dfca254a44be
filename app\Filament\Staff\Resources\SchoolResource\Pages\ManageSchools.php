<?php

namespace App\Filament\Staff\Resources\SchoolResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\SchoolResource;

class ManageSchools extends ManageRecords
{
    protected static string $resource = SchoolResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->successNotification(function () {
            //         return Notification::make()
            //             ->success()
            //             ->title('School Created')
            //             ->body('The school has been created successfully');
            //     }),
        ];
    }
}
