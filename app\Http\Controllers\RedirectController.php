<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;

class RedirectController extends Controller
{
    public function handleRoleRedirect(): RedirectResponse
    {
        $userRole = Auth::user()->role;

        if (in_array($userRole, all_staff_roles())) {
            $staffPanelPath = Filament::getPanel('staff')->getPath();
            $intendedUrl = session('url.intended');

            // Only use intended if it's within the staff panel
            if ($intendedUrl && str_starts_with($intendedUrl, url($staffPanelPath))) {
                return redirect($intendedUrl);
            }

            return redirect($staffPanelPath);
        }

        if (in_array($userRole, [Role::STUDENT])) {

            $studentPanelPath = Filament::getPanel('student')->getPath();
            $intendedUrl = session('url.intended');

            // Only use intended if it's within the student panel
            if ($intendedUrl && str_starts_with($intendedUrl, url($studentPanelPath))) {
                return redirect($intendedUrl);
            }

            return redirect($studentPanelPath);
        }

        Auth::logout();
        abort(403, 'Unauthorized role.');
    }
}
