<?php

namespace App\Filament\Student\Resources;

use App\Enums\Role;
use App\Models\User;
use Filament\Tables\Table;
use App\Enums\AdmissionStatus;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Student\Resources\RegistrationResource\Pages;

class RegistrationResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?int $navigationSort = 2;
    protected static ?string $modelLabel = 'registrations';
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }

    public static function getEloquentQuery(): Builder
    {
          return parent::getEloquentQuery()
            ->with(['registrations' => fn($query) => $query->with(['programme', 'level', 'semester', 'schoolSession', 'portalInvoice'])])
            ->where('users.id', Auth::user()->id)
            ->where('users.role', Role::STUDENT)
            ->limit(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->paginated(false)
            ->recordUrl(null)
            ->recordAction(null)
            ->emptyStateHeading(fn(HasTable $livewire) => self::getEmptyStateHeading($livewire))
            ->emptyStateDescription(fn(HasTable $livewire) => new HtmlString(self::getEmptyStateDescription($livewire)))
            ->description('This section displays your course registrations per semester.')
            ->columns([
                ViewColumn::make('')
                    ->view('filament.tables.registrations'),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRegistrations::route('/'),
        ];
    }
}
