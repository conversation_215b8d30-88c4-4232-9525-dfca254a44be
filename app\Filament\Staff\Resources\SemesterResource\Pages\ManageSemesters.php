<?php

namespace App\Filament\Staff\Resources\SemesterResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\SemesterResource;

class ManageSemesters extends ManageRecords
{
    protected static string $resource = SemesterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->successNotification(function () {
            //         return Notification::make()
            //             ->success()
            //             ->title('Semester Created')
            //             ->body('The semester has been created successfully');
            //     }),
        ];
    }
}
