<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum CourseStatus: string implements HasLabel
{
    case Compulsory = 'C';
    case Elective = 'E';

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::Compulsory => 'Compulsory',
            self::Elective => 'Elective',
        };
    }

    /**
     * Get the Alias for the enum
     */
    public function getAlias(): string
    {
        return match ($this) {
            self::Compulsory => 'C',
            self::Elective => 'E',
        };
    }
}
