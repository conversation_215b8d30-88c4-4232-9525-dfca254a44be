<?php

namespace App\Filament\Staff\Resources;


use Filament\Tables;
use Filament\Forms\Form;
use App\Models\Programme;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\ToggleColumn;
use App\Filament\Staff\Resources\ProgrammeResource\Pages;
use Filament\Forms\Components\Checkbox;

class ProgrammeResource extends Resource
{
    protected static ?string $model = Programme::class;
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of programmes';

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('code')
                    ->required()
                    ->maxLength(15)
                    ->placeholder('5534EF')
                    ->unique(ignoreRecord: true),
                TextInput::make('name')
                    ->required()
                    ->maxLength(40)
                    ->placeholder('English/French'),
                Select::make('first_department_id')
                    ->required()
                    ->label('First department')
                    ->native(false)
                    ->relationship('firstDepartment', 'name')
                    ->placeholder('Select a department'),
                Select::make('second_department_id')
                    ->label('Second department')
                    ->native(false)
                    ->relationship('secondDepartment', 'name')
                    ->placeholder('Select a department'),
                Checkbox::make('is_active')
                    ->label('Active')
                    ->inline(false),
            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated([10, 20, 30, 40, 50])
            ->striped()
            ->recordAction(null)
            ->defaultSort('name')
            ->emptyStateHeading('No Programmes Yet')
            ->emptyStateDescription('Once you create your first programme, it will appear here.')
            ->description('The list of programmes created for efficient academic management.')
            ->searchPlaceholder('Search (programme name)')
            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('code'),
                TextColumn::make('name')
                    ->searchable(),
                ColumnGroup::make('Departments', [
                    TextColumn::make('firstDepartment.name')
                        ->label('First'),
                    TextColumn::make('secondDepartment.name')
                        ->label('Second')
                        ->placeholder('NIL'),
                ])->alignment(Alignment::Center),
                ToggleColumn::make('is_active')
                    ->disabled(fn() => ! main_staff_access())
                    ->label('Active'),
            ])
            ->filters([])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Programme Updated')
                                ->body('The programme has been updated successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Programme?')
                        ->modalDescription('Are you sure you want to delete this programme?')
                        ->modalSubmitActionLabel('Yes, delete it')
                        ->modalCancelActionLabel('No, keep it')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['registrations'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this programme because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Programme Deleted')
                                ->body('The programme has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Programme actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProgrammes::route('/'),
        ];
    }
}
