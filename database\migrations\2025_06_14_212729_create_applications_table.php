<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applications', function (Blueprint $table) {
            $table->id();
            $table->string('number')->unique();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('school_session_id')->nullable()->constrained()->cascadeOnDelete();
            $table->foreignId('programme_id')->nullable()->constrained()->cascadeOnDelete();
            $table->string('secondary_school_attended')->nullable();
            $table->year('secondary_school_graduation_year')->nullable();
            $table->string('jamb_registration_number', 10)->nullable();
            $table->string('exam_board')->nullable();
            $table->json('exam_result')->nullable();
            $table->boolean('is_declared')->default(false);
            $table->integer('screening_score')->nullable();
            $table->tinyInteger('screening_status')->default(0);
            $table->tinyInteger('admission_status')->default(0);
            $table->timestamp('admission_date')->nullable();
            $table->timestamps();
        });
        Schema::create('application_counters', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('last_sequence')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applications');
        Schema::dropIfExists('application_counters');
    }
};
