<?php

namespace App\Jobs;

use App\Services\EventService;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessChargeSuccess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $payload;

    /**
     * Create a new job instance.
     */
    public function __construct(array $payload)
    {
        $this->payload = $payload;
    }


    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            app(EventService::class)->handleChargeSuccess($this->payload);
        } catch (\Exception $e) {
            Log::error('Failed to process charge success webhook', ['error' => $e->getMessage()]);
            $this->fail($e);
        }
    }
}
