<?php
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

$now = Carbon::now();
$hour = $now->format('H');
$today = $now->format('l, F j, Y');

if ($hour < 12) {
    $greeting='Good morning' ;
} elseif ($hour < 18) {
    $greeting='Good afternoon' ;
} else {
    $greeting='Good evening' ; }

    ?>

<div class="hidden sm:flex flex-row text-xs text-gray-600 dark:text-gray-200 order-first">
    <div class="flex flex-col">
        <span>
            {{ $greeting }},
            <strong>{{ explode(' ', Auth::user()->name)[0] }} </strong>&nbsp;

            <x-filament::badge color="gray" style="display: inline-block;">
                {{ Auth::user()->role->getLabel() }}
            </x-filament::badge>
        </span>
    </div>
</div>
