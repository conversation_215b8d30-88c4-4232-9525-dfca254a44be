<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum ExamBoard: string implements HasLabel
{
    case AWAITING_RESULT = 'awaiting_result';
    case WAEC = 'waec';
    case NECO = 'neco';
    case NABTEB = 'nabteb';

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::AWAITING_RESULT => 'Awaiting result',
            self::WAEC => 'WAEC',
            self::NECO => 'NECO',
            self::NABTEB => 'NABTEB',
        };
    }
}
