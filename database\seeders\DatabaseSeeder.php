<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(StateSeeder::class);
        $this->call(LocalGovernmentAreaSeeder::class);
        $this->call(StaffSeeder::class);
        $this->call(NewsSeeder::class);
        $this->call(SchoolSessionSeeder::class);
        $this->call(SemesterSeeder::class);
        $this->call(SemesterScheduleSeeder::class);
        $this->call(LevelSeeder::class);
        $this->call(SchoolSeeder::class);
        $this->call(DepartmentSeeder::class);
        $this->call(CourseSeeder::class);
        $this->call(AssessmentSeeder::class);
        $this->call(GradeSeeder::class);
        $this->call(ProgrammeSeeder::class);
        $this->call(AdmissionCounterSeeder::class);
        // $this->call(StudentSeeder::class);
    }
}
