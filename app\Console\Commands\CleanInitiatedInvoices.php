<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Enums\InvoiceStatus;
use Illuminate\Console\Command;

class CleanInitiatedInvoices extends Command
{
    protected $signature = 'invoices:clean-initiated-invoices';
    protected $description = 'Clean initiated invoices that have not been paid after 15 minutes.';


    public function handle()
    {
        $invoices = Invoice::query()
            ->where('payment_success', false)
            ->where('invoice_status', InvoiceStatus::INITIATED)
            ->where('created_at', '<=', now()->subMinutes(15))
            ->get();

        $count = $invoices->count();

        foreach ($invoices as $invoice) {
            $invoice->delete();
        }

        $this->info("Cleaned $count stale unpaid invoices (older than 15 minutes).");
    }
}
