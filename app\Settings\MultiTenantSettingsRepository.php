<?php

namespace App\Settings;

use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Builder;
use Spatie\LaravelSettings\SettingsRepositories\DatabaseSettingsRepository;

class MultiTenantSettingsRepository extends DatabaseSettingsRepository
{
    protected $defaults = [];

    public function __construct(array $config)
    {
        parent::__construct($config);
        $this->loadDefaults();
    }

    public function getBuilder(): Builder
    {
        return parent::getBuilder();
    }

    protected function loadDefaults(): void
    {
        $cacheKey = 'settings_defaults';

        $this->defaults = Cache::remember($cacheKey, 3600, function () {
            $defaults = [];
            $settingsClasses = config('settings.settings', []);

            foreach ($settingsClasses as $settingsClass) {
                if (method_exists($settingsClass, 'default')) {
                    $group = $settingsClass::group();
                    $defaults[$group] = $settingsClass::default();
                }
            }

            return $defaults;
        });
    }

    public function getPropertiesInGroup(string $group): array
    {
        $properties = parent::getPropertiesInGroup($group);
        $defaults = $this->defaults[$group] ?? [];

        return array_merge($defaults, $properties);
    }

    public function checkIfPropertyExists(string $group, string $name): bool
    {
        return parent::checkIfPropertyExists($group, $name)
            || isset($this->defaults[$group][$name]);
    }

    public function getPropertyPayload(string $group, string $name)
    {
        try {
            return parent::getPropertyPayload($group, $name);
        } catch (\Exception) {
            return $this->defaults[$group][$name] ?? null;
        }
    }

    public function createProperty(string $group, string $name, $payload): void
    {
        $this->getBuilder()->create($this->buildPropertyData($group, $name, $payload));
    }

    public function updatePropertiesPayload(string $group, array $properties): void
    {
        $propertiesInBatch = collect($properties)
            ->map(fn($payload, $name) => $this->buildPropertyData($group, $name, $payload))
            ->values()
            ->toArray();

        $this->getBuilder()->upsert(
            $propertiesInBatch,
            ['group', 'name'],
            ['payload', 'locked']
        );
    }

    protected function buildPropertyData(string $group, string $name, $payload): array
    {
        return [
            'group' => $group,
            'name' => $name,
            'payload' => $this->encode($payload),
            'locked' => false,
        ];
    }

    public function clearCache(): void
    {
        Cache::forget('settings_defaults');
    }
}
