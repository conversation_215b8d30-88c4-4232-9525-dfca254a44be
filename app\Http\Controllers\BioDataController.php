<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Models\User;
use App\Models\SemesterSchedule;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;

class BioDataController extends Controller
{

    public function print(User $student)
    {
        return view('filament.documents.bio-data', $this->getBioData($student));
    }

    public function download(User $student)
    {
        return Pdf::view('filament.documents.bio-data', $this->getBioData($student))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name('Bio-data - ' . $student->name . '.pdf')
            ->download();
    }

    private function getBioData(User $student)
    {
        $collegeSettings = app(CollegeSettings::class);

        $semesterSchedule = SemesterSchedule::where('school_session_id', $student->application->schoolSession->id)->orderBy('semester_start', 'asc')->first();
        $semesterStart = $semesterSchedule && $semesterSchedule->semester_start ? $semesterSchedule->semester_start : null;

        $registrar = User::where('role', Role::REGISTRAR)->first();

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
            'semesterStart' => $semesterStart,
            'registrar' => $registrar
        ];
    }
}
