@props(['title', 'intro', 'philosophy', 'objectives', 'courses'])

<x-layouts.app>
    <x-slot name="title">{{ $title }}</x-slot>

    <section class="bg-white py-16">
        <div class="max-w-3xl mx-auto px-4 sm:px-6">
            <div class="text-center mb-12">
                <h1 class="text-4xl pb-2 font-bold text-[#96281B]">{{ $title }}</h1>
                <p class="text-gray-400 leading-relaxed">
                    NIGERIA CERTIFICATE IN EDUCATION MINIMUM STANDARDS (2020 EDITION)
                </p>
            </div>

            {{-- Introduction --}}
            <x-department-section title="Introduction" :body="$intro" />

            {{-- Philosophy --}}
            <x-department-section title="Philosophy" :body="$philosophy" />

            {{-- Objectives --}}
            <div class="mb-12">
                <h2 class="text-2xl font-semibold text-[#96281B] mb-4">Objectives of the Programme</h2>
                <p class="text-gray-700 leading-relaxed pb-2">
                    The objectives of the programme are to:
                </p>
                <ul class="list-disc text-gray-700 space-y-2">                    
                    @foreach ($objectives as $item)
                        <li>{{ $item }}</li>
                    @endforeach
                </ul>
            </div>

            {{-- Admission Requirements --}}
            <x-admission-requirements />

            {{-- Course Outline --}}
            <x-course-outline :courses="$courses" />
        </div>
    </section>
</x-layouts.app>
