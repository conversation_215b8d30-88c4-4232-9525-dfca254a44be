# ------------------------
# App Info
# ------------------------
APP_NAME=
APP_ENV=local
APP_KEY=
APP_URL=
APP_TIMEZONE=Africa/Lagos
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

# ------------------------
# Contacts
# ------------------------
APP_EMAIL=
APP_PHONE=+2347067689462
ADMIN_EMAIL=<EMAIL>
DEVELOPER_EMAIL=<EMAIL>

# ------------------------
# Logging
# ------------------------
LOG_CHANNEL=stack
LOG_STACK=single
LOG_LEVEL=debug
LOG_DEPRECATIONS_CHANNEL=null

# ------------------------
# PHP & Security
# ------------------------
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=12

# ------------------------
# Maintenance Mode
# ------------------------
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

# ------------------------
# Database
# ------------------------
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

# ------------------------
# Sessions
# ------------------------
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# ------------------------
# Cache
# ------------------------
CACHE_STORE=database
CACHE_PREFIX=

# ------------------------
# Queue
# ------------------------
QUEUE_CONNECTION=database

# ------------------------
# Filesystem
# ------------------------
FILESYSTEM_DISK=public

# ------------------------
# Broadcasting (Reverb)
# ------------------------
BROADCAST_CONNECTION=reverb
REVERB_APP_ID=
REVERB_APP_KEY=
REVERB_APP_SECRET=
REVERB_HOST=
REVERB_PORT=8080
REVERB_SCHEME=https

VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"

# ------------------------
# Redis
# ------------------------
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# ------------------------
# Analytics
# ------------------------
ANALYTICS_PROPERTY_ID=

# ------------------------
# Mail (Mailtrap)
# ------------------------
MAIL_MAILER=mailtrap
# MAILTRAP_HOST=send.api.mailtrap.io
MAILTRAP_HOST=sandbox.api.mailtrap.io
MAILTRAP_API_KEY=
MAILTRAP_INBOX_ID=
MAIL_FROM_ADDRESS="${APP_EMAIL}"
MAIL_FROM_NAME="${APP_NAME}"

# ------------------------
# Paystack
# ------------------------
PAYSTACK_API_URL=https://api.paystack.co
PAYSTACK_MERCHANT_EMAIL="${APP_EMAIL}"
PAYSTACK_PUBLIC_KEY=
PAYSTACK_SECRET_KEY=

# ------------------------
# Captcha
# ------------------------
NOCAPTCHA_SITEKEY=
NOCAPTCHA_SECRET=

# ------------------------
# GitHub (Easy-footer)
# ------------------------
GITHUB_REPOSITORY=
GITHUB_TOKEN=
GITHUB_CACHE_TTL=3600

# ------------------------
# AWS (Optional)
# ------------------------
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# ------------------------
# Vite
# ------------------------
VITE_APP_NAME="${APP_NAME}"
