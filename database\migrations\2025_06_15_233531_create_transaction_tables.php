<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transaction_counters', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('last_sequence')->default(0);
            $table->timestamps();
        });

        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->nullable()->constrained()->nullOnDelete();
            $table->string('number')->unique();
            $table->unsignedBigInteger('amount');
            $table->tinyInteger('transaction_status')->default(0);
            $table->tinyInteger('payment_method')->nullable();
            $table->string('payment_channel')->nullable();
            $table->tinyInteger('transaction_type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transaction_counters');
        Schema::dropIfExists('transactions');
    }
};
