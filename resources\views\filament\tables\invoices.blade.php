@php
use App\Services\MoneyService;

    $moneyService = new MoneyService();
    $total = $moneyService->toNaira($record->total_amount);
    $paid = $moneyService->toNaira($record->paid);
    $due = $moneyService->toNaira($record->due);

    $paidPercentage = $total > 0 ? round($paid / $total * 100) : 0;
    $duePercentage = $total > 0 ? round($due / $total * 100) : 0;

@endphp  
<div>

<table class="table-auto border w-full text-left">
    <thead>
        <tr class="bg-gray-100">
            <th class="border px-4 py-2 text-center" colspan="4">Summary</th>
        </tr>
        <tr class="bg-gray-100">
            <th class="border px-4 py-2">Descriptions</th>
            <th class="border px-4 py-2">Total</th>
            <th class="border px-4 py-2">Paid</th>
            <th class="border px-4 py-2">Due</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="border px-4 py-2">
                @if(is_array($record->description))
                    <ul class="list-disc pl-5 space-y-1">
                        @foreach($record->description as $item)
                            <li>{{ $item['item'] }} - ₦{{ number_format($item['amount']) }}</li>
                        @endforeach
                    </ul>
                @else
                    {{ $record->description }}
                @endif
            </td>            
            <td class="border px-4 py-2">₦{{ number_format($total, 0) }}</td>
            <td class="border px-4 py-2">₦{{ number_format($paid, 0) }} ({{ $paidPercentage }}%) </td>
            <td class="border px-4 py-2">₦{{ number_format($due, 0) }} ({{ $duePercentage }}%) </td>
        </tr>
    </tbody>
</table>

<table class="table-auto border w-full text-left mt-4">
    <thead>
        <tr class="bg-gray-100">
            <th class="border px-4 py-2 text-center" colspan="6">Transactions</th>
        </tr>
        <tr class="bg-gray-100">           
            <th class="border px-4 py-2">Invoice number</th>
            <th class="border px-4 py-2">Amount</th>
            <th class="border px-4 py-2">Status</th>
            <th class="border px-4 py-2">Date</th>
            <th class="border px-4 py-2"></th>
        </tr>
    </thead>
    <tbody>
        @foreach ($filteredTransactions as $transaction)
            <tr>
                <td class="border px-4 py-2">{{ $transaction->number }}</td>
                <td class="border px-4 py-2">₦{{ number_format($transaction->amount, 0) }}</td>
                <td class="border px-4 py-2 text-{{ $transaction->transaction_status->getTailwindColor()}}"><x-filament::badge>{{ $transaction->transaction_status->getLabel() }}</x-filament::badge></td>
                <td class="border px-4 py-2">{{ $transaction->paid_at? $transaction->paid_at->format('d M, Y'): 'NIL'  }}</td>
                <td class="border px-4 py-2">
                    <x-filament::dropdown>
                        <x-slot name="trigger">                            
                            <x-filament::icon-button
                                icon="heroicon-m-document-text"
                                color="primary"
                                label="Export invoice"
                            />
                        </x-slot>
                        <x-filament::dropdown.list>
                            <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printInvoice({{ $transaction->id }}, {{ $due }})">                                                              
                                    Print                              
                            </x-filament::dropdown.list.item>
                            <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadInvoice({{ $transaction->id }}, {{ $due }})">                                                          
                                    Download                                
                            </x-filament::dropdown.list.item>
                        </x-filament::dropdown.list>
                    </x-filament::dropdown>               
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
</div>
