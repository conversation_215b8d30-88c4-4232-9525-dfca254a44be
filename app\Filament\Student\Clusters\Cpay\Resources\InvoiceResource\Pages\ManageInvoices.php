<?php

namespace App\Filament\Student\Clusters\Cpay\Resources\InvoiceResource\Pages;

use App\Filament\Student\Clusters\Cpay\Resources\InvoiceResource;
use Filament\Resources\Pages\ManageRecords;

class ManageInvoices extends ManageRecords
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
