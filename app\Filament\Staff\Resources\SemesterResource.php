<?php

namespace App\Filament\Staff\Resources;


use Filament\Tables;
use App\Models\Semester;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use App\Filament\Staff\Resources\SemesterResource\Pages;

class SemesterResource extends Resource
{
    protected static ?string $model = Semester::class;
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of semesters';

    public static function canAccess(): bool
    {
        return management_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(15)
                    ->label('Semester')
                    ->placeholder('First semester')
                    ->unique(ignoreRecord: true),
            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('name')
            ->emptyStateHeading('No Semesters Yet')
            ->emptyStateDescription('Once you create your first semester, it will appear here.')
            ->description('The list of semesters created for efficient academic management.')

            ->columns([
                TextColumn::make('#')
                    ->width('2rem')
                    ->rowIndex(),
                TextColumn::make('name'),
            ])
            ->filters([
                //
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Semester Updated')
                                ->body('The semester has been updated successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Semester?')
                        ->modalDescription('Are you sure you want to delete this semester?')
                        ->modalSubmitActionLabel('Yes, delete it')
                        ->modalCancelActionLabel('No, keep it')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['registrations', 'courses'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this semester because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Semester Deleted')
                                ->body('The semester has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Semester actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSemesters::route('/'),
        ];
    }
}
