<x-department-page
    title="Department of Computer Science"
    :intro="'The Department of Computer Science at Raphat College of Education Obaagun is focused on training teachers with strong knowledge of computing principles, programming, and digital literacy. The programme covers both theory and hands-on skills in hardware, software, and ICT tools. Graduates are prepared to teach computer studies effectively at the basic education level and adapt to the evolving tech landscape.'"
    :philosophy="'Nigeria cannot afford to ignore the role which computer literacy plays in achieving the national goal of technological development.  Hence, she has resolved to introduce computer education in primary and secondary schools.  For meaningful teaching of computer science in our primary and secondary schools, there is a need to produce professional teachers in the discipline.  Hitherto there had been provision for the training of computer scientists in the universities and the polytechnics, but little attention was paid to the training of teachers in computer education.  There is now an urgent need for the Colleges of Education to offer computer studies as a subject in the programme of professional preparation of teachers.'"
    :objectives="[
        'Effectively teach Computer Studies at the Primary, Secondary and Technical Schools.',
        'Write Computer program and process data with maximum speed and accuracy; Demonstrate reasonably high level of competence in preparation for further studies in computer science education.',
        'Motivate pupils’ and learners’ interest in the study of computer studies by adopting appropriate ICT teaching and learning strategies.',
        'Apply the use of computer as an aid in daily life activities.',
        'Maintain Computer Hardware.'
    ]"
    :courses="[
     'Year 1' => [
            'First Semester' => [
                ['code' => 'CSC 111', 'title' => 'Introduction to Computer Science', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 112', 'title' => 'BASIC Programming Language', 'credit' => 2, 'status' => 'C'],
                ['code' => 'CSC 113', 'title' => 'Computer Operations/Application and Electronic Data Processing', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 114', 'title' => 'Number Systems', 'credit' => 1, 'status' => 'C'],
            ],
            'Second Semester' => [
                ['code' => 'CSC 121', 'title' => 'Introduction to Micro processor', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 122', 'title' => 'Application Packages (MS Word, MS Excel & MS Power point)', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 123', 'title' => 'Computer Maintenance / Troubleshooting', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 124', 'title' => 'The Teaching of Computer Science', 'credit' => 1, 'status' => 'C'],
            ],
        ],

        'Year 2' => [
            'First Semester' => [
                ['code' => 'CSC 211', 'title' => 'Computer Logic', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 212', 'title' => 'Database Management', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 213', 'title' => 'Data Structures', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 214', 'title' => 'C Programming Language', 'credit' => 2, 'status' => 'C'],
            ],
            'Second Semester' => [
                ['code' => 'CSC 221', 'title' => 'Introduction to Web Design & Development (HTML & CSS)', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 222', 'title' => 'Operating Systems', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 223', 'title' => 'Introduction to Numerical Methods', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 224', 'title' => 'SIWES', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 225', 'title' => 'System Analysis and Design', 'credit' => 1, 'status' => 'C'],
            ],
        ],

        'Year 3' => [
            'First Semester' => [
                ['code' => 'EDU 311', 'title' => 'Teaching Practice', 'credit' => 6, 'status' => 'C'],
            ],
            'Second Semester' => [
                ['code' => 'CSC 321', 'title' => 'Advanced Level Programming Language (JAVA, C++, VBASIC, VCOBOL)', 'credit' => 2, 'status' => 'C'],
                ['code' => 'CSC 322', 'title' => 'Seminars and Field Trip', 'credit' => 1, 'status' => 'C'],
                ['code' => 'CSC 323', 'title' => 'Introduction to E-learning Design and Development (Blended Approach)', 'credit' => 1, 'status' => 'E'],
                ['code' => 'CSC 324', 'title' => 'Computer Graphics and Desktop Publishing', 'credit' => 1, 'status' => 'E'],
                ['code' => 'CSC 325', 'title' => 'Introduction to Computer Networking', 'credit' => 1, 'status' => 'C'],
            ],
        ],

    ]"
/>
