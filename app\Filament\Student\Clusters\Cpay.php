<?php

namespace App\Filament\Student\Clusters;

use App\Enums\Role;
use App\Enums\AdmissionStatus;
use Filament\Clusters\Cluster;
use Illuminate\Support\Facades\Auth;

class Cpay extends Cluster
{
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationLabel = 'Cpay™';
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }
}
