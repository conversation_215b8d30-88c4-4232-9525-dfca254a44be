<?php

namespace App\Filament\Student\Pages\Auth;

use Filament\Forms\Form;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Pages\Auth\EditProfile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Components\StudentForm;
use Filament\Notifications\Notification;
use App\Filament\Student\Pages\Dashboard;
use Illuminate\Contracts\Support\Htmlable;

class StudentProfile extends EditProfile
{

    public function getTitle(): string | Htmlable
    {
        return 'Student Bio-data';
    }

    public function getSubheading(): string | Htmlable
    {
        return new HtmlString('<span style="color:red;">Only the ICT Department can update bio-data. Contact them for changes.</span>');
    }

    public static function getSlug(): string
    {
        return 'bio-data';
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $student = Auth::user();
        $data['guardian']['title'] = $student->guardian?->title ?? null;
        $data['guardian']['relationship'] = $student->guardian?->relationship ?? null;
        $data['guardian']['occupation'] = $student->guardian?->occupation ?? null;
        $data['guardian']['phone'] = $student->guardian?->phone ?? null;
        $data['guardian']['first_name'] = $student->guardian?->first_name ?? null;
        $data['guardian']['last_name'] = $student->guardian?->last_name ?? null;
        $data['application']['secondary_school_attended'] = $student->application?->secondary_school_attended ?? null;
        $data['application']['secondary_school_graduation_year'] = $student->application?->secondary_school_graduation_year ?? null;
        $data['application']['jamb_registration_number'] = $student->application?->jamb_registration_number ?? null;
        $data['application']['jamb_score'] = $student->application?->jamb_score ?? null;
        $data['application']['exam_board'] = $student->application?->exam_board ?? null;
        $data['application']['exam_result'] =  $student->application?->exam_result ?? [];
        $data['activeSchoolSession'] = $student->activeRegistration?->schoolSession?->name ?? null;
        $data['activeSemester'] = $student->activeRegistration?->semester?->name ?? null;
        $data['activeLevel'] = $student->activeRegistration?->level?->name ?? null;
        $data['activeProgramme'] = $student->activeRegistration?->programme?->name ?? null;

        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                StudentForm::schema(),
            );
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        DB::transaction(function () use ($record, $data) {
            $userData = collect($data)->except(['application', 'guardian'])->toArray();
            $record->update($userData);

            if (isset($data['guardian'])) {
                $record->guardian()->updateOrCreate([], $data['guardian']);
            }

            if (isset($data['application'])) {
                $record->application()->updateOrCreate([], $data['application']);
            }
        });

        return $record->fresh();
    }

    protected function getSavedNotification(): ?Notification
    {

        return Notification::make()
            ->success()
            ->title('Bio-data Updated')
            ->body('Your bio-data has been saved successfully');
    }


    protected function getFormActions(): array
    {
        return [
            $this->getCancelFormAction()
                ->label('Back'),
        ];
    }

    protected function getRedirectUrl(): ?string
    {
        return Dashboard::getUrl(['panel' => 'student']);
    }
}
