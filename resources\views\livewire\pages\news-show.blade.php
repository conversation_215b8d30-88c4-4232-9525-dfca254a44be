<section class="bg-white py-10">
    <div class="max-w-full sm:max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-semibold text-[#96281B] mb-6 text-center">Campus News</h2>
        <div class="bg-gray-100 rounded-sm shadow p-4">
            <img src="{{ Storage::url($newsPost->image) }}" alt="{{ $newsPost->title }}" class="w-full h-40 object-cover rounded mb-3">
            <h3 class="text-lg font-semibold text-[#96281B]">{{ $newsPost->title }}</h3>
            <p class="text-sm text-gray-600 mb-2">{{ \Carbon\Carbon::parse($newsPost->date)->toFormattedDateString() }}</p>
            <div class="prose prose-sm sm:prose lg:prose-lg max-w-none text-gray-800">
                {!! str($newsPost->content)->markdown()->sanitizeHtml() !!}
            </div>
        </div>
    </div>
</section>

