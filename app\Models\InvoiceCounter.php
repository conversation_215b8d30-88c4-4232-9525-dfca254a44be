<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class InvoiceCounter extends Model
{
    public static function getNextSequence(): int
    {
        return DB::transaction(function () {
            // Lock the row based on subscriberId if provided, or lock on null if not
            $counter = self::lockForUpdate()
                ->first();

            if (!$counter) {
                $counter = self::create([
                    'last_sequence' => 1,
                ]);
                return 1;
            }

            $counter->increment('last_sequence');
            return $counter->last_sequence;
        });
    }
}
