<?php

use Illuminate\Support\Facades\Schedule;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use App\Console\Commands\CleanInitiatedInvoices;


Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');


Schedule::command(CleanInitiatedInvoices::class)
    ->everyFiveMinutes()
    ->appendOutputTo(storage_path('logs/clean_initiated_invoices.log'));
