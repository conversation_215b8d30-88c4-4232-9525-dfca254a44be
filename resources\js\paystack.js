document.addEventListener('livewire:init', () => {
  Livewire.on('paystack-popup', ({ accessCode, invoiceId }) => {
    const popup = new PaystackPop();
    popup.resumeTransaction(accessCode, {
      onSuccess: function() {
        Livewire.dispatch("payment-success", {invoiceId: invoiceId});
      },
      onCancel: function() {
        Livewire.dispatch('payment-canceled', {invoiceId: invoiceId});
      }
    });
  });
});

