{"__meta": {"id": "01K5BAET30FHNYXFCK3KYBEX8X", "datetime": "2025-09-17 08:50:35", "utime": **********.875386, "method": "GET", "uri": "/student/cpay", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.468367, "end": **********.875401, "duration": 0.40703392028808594, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.468367, "relative_start": 0, "end": **********.787081, "relative_end": **********.787081, "duration": 0.***************, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.787096, "relative_start": 0.****************, "end": **********.875403, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "88.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.797196, "relative_start": 0.****************, "end": **********.800115, "relative_end": **********.800115, "duration": 0.0029191970825195312, "duration_str": "2.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.848409, "relative_start": 0.*****************, "end": **********.848825, "relative_end": **********.848825, "duration": 0.00041604042053222656, "duration_str": "416μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.871938, "relative_start": 0.*****************, "end": **********.871999, "relative_end": **********.871999, "duration": 6.103515625e-05, "duration_str": "61μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7095448, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03118, "accumulated_duration_str": "31.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.80895, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z' limit 1", "type": "query", "params": [], "bindings": ["A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.809715, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 9.108}, {"sql": "select * from `users` where `id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.819403, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 9.108, "width_percent": 2.213}, {"sql": "select * from `applications` where `applications`.`user_id` = 22 and `applications`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/FeeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\FeeResource.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Clusters/Cluster.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Clusters\\Cluster.php", "line": 51}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 50}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Clusters/Cluster.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Clusters\\Cluster.php", "line": 40}], "start": **********.827206, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "FeeResource.php:39", "source": {"index": 21, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/FeeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\FeeResource.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FClusters%2FCpay%2FResources%2FFeeResource.php&line=39", "ajax": false, "filename": "FeeResource.php", "line": "39"}, "connection": "racoed", "explain": null, "start_percent": 11.321, "width_percent": 2.309}, {"sql": "select count(*) as aggregate from `fees` where `is_scheduled` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/FeeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\FeeResource.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Clusters/Cluster.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Clusters\\Cluster.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 50}], "start": **********.83237, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "FeeResource.php:51", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/FeeResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\FeeResource.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FClusters%2FCpay%2FResources%2FFeeResource.php&line=51", "ajax": false, "filename": "FeeResource.php", "line": "51"}, "connection": "racoed", "explain": null, "start_percent": 13.631, "width_percent": 11.321}, {"sql": "select count(*) as aggregate from `invoices` where `invoice_status` = 3 and ((`invoices`.`payable_type` = 'App\\\\Models\\\\Fee' and exists (select * from `fees` where `invoices`.`payable_id` = `fees`.`id` and `user_id` = 22)) or (`invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and exists (select * from `registrations` where `invoices`.`payable_id` = `registrations`.`id` and `user_id` = 22))) and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3, "App\\Models\\Fee", 22, "App\\Models\\Registration", 22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\InvoiceResource.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Clusters/Cluster.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Clusters\\Cluster.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 50}], "start": **********.839602, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "InvoiceResource.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/InvoiceResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\InvoiceResource.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FClusters%2FCpay%2FResources%2FInvoiceResource.php&line=56", "ajax": false, "filename": "InvoiceResource.php", "line": "56"}, "connection": "racoed", "explain": null, "start_percent": 24.952, "width_percent": 6.222}, {"sql": "select count(*) as aggregate from `registrations` where `user_id` = 22", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/PortalResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\PortalResource.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Clusters/Cluster.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Clusters\\Cluster.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/HasSubNavigation.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Pages\\Concerns\\HasSubNavigation.php", "line": 50}], "start": **********.8434832, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PortalResource.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Student/Clusters/Cpay/Resources/PortalResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Student\\Clusters\\Cpay\\Resources\\PortalResource.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStudent%2FClusters%2FCpay%2FResources%2FPortalResource.php&line=60", "ajax": false, "filename": "PortalResource.php", "line": "60"}, "connection": "racoed", "explain": null, "start_percent": 31.174, "width_percent": 2.021}, {"sql": "update `sessions` set `payload` = 'YTo4OntzOjY6Il90b2tlbiI7czo0MDoiOXNWbnViV0VtTnNpV0RnQ2RTeW9jWW9kS1ZxYXBzdVNLZTVKd3Q5OSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czoyNDoiaHR0cHM6Ly9yYWNvZWQudGVzdC9oZWxwIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L3N0dWRlbnQvY3BheSI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjIyO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkQkZsdEhRL0Jselpic3NvYnJzeExCLjk0U2NuVGlkZXpDaHVjUWxBcmZ0M0tyLmRCYlFtQWkiO3M6NjoidGFibGVzIjthOjE6e3M6Mjc6Ik1hbmFnZVJlZ2lzdHJhdGlvbnNfZmlsdGVycyI7Tjt9czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 22, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36' where `id` = 'A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z'", "type": "query", "params": [], "bindings": ["YTo4OntzOjY6Il90b2tlbiI7czo0MDoiOXNWbnViV0VtTnNpV0RnQ2RTeW9jWW9kS1ZxYXBzdVNLZTVKd3Q5OSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czoyNDoiaHR0cHM6Ly9yYWNvZWQudGVzdC9oZWxwIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHBzOi8vcG9ydGFsLnJhY29lZC50ZXN0L3N0dWRlbnQvY3BheSI7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjIyO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkQkZsdEhRL0Jselpic3NvYnJzeExCLjk0U2NuVGlkZXpDaHVjUWxBcmZ0M0tyLmRCYlFtQWkiO3M6NjoidGFibGVzIjthOjE6e3M6Mjc6Ik1hbmFnZVJlZ2lzdHJhdGlvbnNfZmlsdGVycyI7Tjt9czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YTowOnt9fQ==", **********, 22, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.850236, "duration": 0.020239999999999998, "duration_str": "20.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "racoed", "explain": null, "start_percent": 33.194, "width_percent": 64.913}, {"sql": "select * from `sessions` where `id` = 'A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z' limit 1", "type": "query", "params": [], "bindings": ["A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/php-debugbar/php-debugbar/src/DebugBar/DebugBar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php", "line": 446}], "start": **********.8731718, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 98.108, "width_percent": 1.892}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://portal.racoed.test/student/cpay", "action_name": "filament.student.cpay", "controller_action": "App\\Filament\\Student\\Clusters\\Cpay", "uri": "GET student/cpay", "domain": "https://portal.racoed.test", "controller": "App\\Filament\\Student\\Clusters\\Cpay@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "student/", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:student, <PERSON>lum<PERSON>\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "404ms", "peak_memory": "10MB", "response": "Redirect to https://portal.racoed.test/student/cpay/portal", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6InJUZzNrWEFjSjBONm4ycS83NUZ4SWc9PSIsInZhbHVlIjoic3hEeENYUUx6cFd4VllSWEwxdXRlb1BOUWlPR0xBN1JoYnlnS2hMdGdzWGRWZDhTV0VZcXBuR0UybCszT0RxMHR2ZjZWWlVaaDhiaHhzZDNGWk9HWC8wZUlQVTZoWndMSElrNmt4ZzJ3WFlPUTkrV29BdmE0UHFPYzA4M1hQbkEiLCJtYWMiOiI3ODI4NGViODBlYzQ2Mzk0Y2NiMDMyMDAxNThlZjM0ZTEzMTQwMjM1ZTU0Yzc2NGI3NDA0NjliMjBlMjA5Mjg1IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6ImszUFF5RytUbnQrMFdTMWhENFlIYVE9PSIsInZhbHVlIjoiczF6enlneUNsYUVjL0Rpek9UaDBvV1p0Y2hxczlNdk4xNFRveEpFV0IxdTlQMTFoRE5sSGRGb1BzRGduTnkyOWxPMUk1NUNYRERib1Baa0lqV3VxOGw2cDc0blAxN1RvMzVsRktRdGVYOHpJU2FXa3QzelplZm5rckVhdS9KVWEiLCJtYWMiOiJiZGIyNzIxM2YxMjI0ZjQwOGJiNmYyOWQwM2MwMWZlNTBhYTViNTk0YWQ1YmQ0MTY1YjRhYzc5ZWZkNmYyZTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://portal.racoed.test/student/admissions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-971479980 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9sVnubWEmNsiWDgCdSyocYodKVqapsuSKe5Jwt99</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A2XjPYR1Xlp6h05tbmS4KkiVQ7fWRCAnvz89rr2z</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971479980\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2049833097 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 07:50:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">https://portal.racoed.test/student/cpay/portal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"457 characters\">XSRF-TOKEN=eyJpdiI6Iml5RG51UVp5eVRUK09NR0U2QzVVcFE9PSIsInZhbHVlIjoiTFR6S2lUTXRweDVMNFFyM0VrTmhNdTN6Z1FOdzdkNURDYW5OMm5GM012dEp2RzIzUWxUcnJoTTJxdTFhL0JvMHJNMndUWFRqelpaRUJLc1RYNXBjR2tjR1o3R3I5RkppenIydDVySlF6ZFU3MXpvV0hQNVZ1K1l2Z2dXQUZRalAiLCJtYWMiOiI5NTE0MGFiNGVhYzRjYTE1NjZkMDdmZGRlYzY4MTdhYTY1ZmZjZTc3MWQzZTc0MWZkNDAxZTUwNjgxOTJlZGFiIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 09:50:35 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"471 characters\">racoed_session=eyJpdiI6Ikh1TjFqZXM0L2oyZmN2aTViTVhSL3c9PSIsInZhbHVlIjoiWEkrS0FYck5wLzdTZm5Wc01SQjZKNUw5MFJXbXVLT3E3MHFPdHJUMUFraUZZOHl5QWM1a2owOFZ5a3hlNUFsTlhkU01LMU00eGUvN3lIVnl2Y3dBRlVnMjhVdVFoODFDUU1WenQxelBHVDZuVWR0MEJDbFowYUtham0vbzE4UUgiLCJtYWMiOiJhN2JiYzI0NTlhNmE3ZTMxOTJiZWI2YWZiODgxOWRjNjQwOWJmNjdhMTJiYjI3ODY0OGRjY2RhNTE5YzlmZTI1IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 09:50:35 GMT; Max-Age=7200; path=/; domain=.racoed.test; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6Iml5RG51UVp5eVRUK09NR0U2QzVVcFE9PSIsInZhbHVlIjoiTFR6S2lUTXRweDVMNFFyM0VrTmhNdTN6Z1FOdzdkNURDYW5OMm5GM012dEp2RzIzUWxUcnJoTTJxdTFhL0JvMHJNMndUWFRqelpaRUJLc1RYNXBjR2tjR1o3R3I5RkppenIydDVySlF6ZFU3MXpvV0hQNVZ1K1l2Z2dXQUZRalAiLCJtYWMiOiI5NTE0MGFiNGVhYzRjYTE1NjZkMDdmZGRlYzY4MTdhYTY1ZmZjZTc3MWQzZTc0MWZkNDAxZTUwNjgxOTJlZGFiIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 09:50:35 GMT; domain=.racoed.test; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">racoed_session=eyJpdiI6Ikh1TjFqZXM0L2oyZmN2aTViTVhSL3c9PSIsInZhbHVlIjoiWEkrS0FYck5wLzdTZm5Wc01SQjZKNUw5MFJXbXVLT3E3MHFPdHJUMUFraUZZOHl5QWM1a2owOFZ5a3hlNUFsTlhkU01LMU00eGUvN3lIVnl2Y3dBRlVnMjhVdVFoODFDUU1WenQxelBHVDZuVWR0MEJDbFowYUtham0vbzE4UUgiLCJtYWMiOiJhN2JiYzI0NTlhNmE3ZTMxOTJiZWI2YWZiODgxOWRjNjQwOWJmNjdhMTJiYjI3ODY0OGRjY2RhNTE5YzlmZTI1IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 09:50:35 GMT; domain=.racoed.test; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049833097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-4712750 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9sVnubWEmNsiWDgCdSyocYodKVqapsuSKe5Jwt99</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"24 characters\">https://racoed.test/help</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://portal.racoed.test/student/cpay</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$BFltHQ/BlzZbssobrsxLB.94ScnTidezChucQlArft3Kr.dBbQmAi</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageRegistrations_filters</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4712750\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://portal.racoed.test/student/cpay", "action_name": "filament.student.cpay", "controller_action": "App\\Filament\\Student\\Clusters\\Cpay"}, "badge": "302 Found"}}