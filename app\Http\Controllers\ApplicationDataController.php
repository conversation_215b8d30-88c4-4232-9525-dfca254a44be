<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;

class ApplicationDataController extends Controller
{

    public function print(User $student)
    {
        return view('filament.documents.application-data', $this->getApplicationFormData($student));
    }

    public function download(User $student)
    {
        return Pdf::view('filament.documents.application-data', $this->getApplicationFormData($student))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name('Application data - ' . $student->name . '.pdf')
            ->download();
    }

    private function getApplicationFormData(User $student)
    {
        $collegeSettings = app(CollegeSettings::class);

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
        ];
    }
}
