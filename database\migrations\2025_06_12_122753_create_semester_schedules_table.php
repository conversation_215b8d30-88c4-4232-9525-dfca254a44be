<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('semester_schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('school_session_id')->constrained()->cascadeOnDelete();
            $table->unsignedBigInteger('semester_id')->constrained()->cascadeOnDelete();
            $table->timestamp('semester_start');
            $table->timestamp('semester_end');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('semester_schedules');
    }
};
