<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Seeder;
use App\Models\Programme;

class ProgrammeSeeder extends Seeder
{
    public function run()
    {
        $combinations = [
            ['name' => 'Business Education (DM)', 'code' => '526243E', 'first' => 'BED', 'second' => null],
            ['name' => 'Computer Education/Biology', 'code' => '526239H', 'first' => 'CSC', 'second' => 'BIO'],
            ['name' => 'Computer Education/Chemistry', 'code' => '526237A', 'first' => 'CSC', 'second' => 'CHE'],
            ['name' => 'Computer Education/Physics', 'code' => '526238J', 'first' => 'CSC', 'second' => 'PHY'],
            ['name' => 'English/Economics', 'code' => '526231B', 'first' => 'ENG', 'second' => 'ECO'],
            ['name' => 'English/French', 'code' => '526235E', 'first' => 'ENG', 'second' => 'FRE'],
            ['name' => 'English/Political Science', 'code' => '526232K', 'first' => 'ENG', 'second' => 'POL'],
            ['name' => 'English/Social Studies', 'code' => '526233I', 'first' => 'ENG', 'second' => 'SOS'],
            ['name' => 'French/Yoruba', 'code' => '526236C', 'first' => 'FRE', 'second' => 'YOR'],
            ['name' => 'Mathematics/Biology', 'code' => '526242G', 'first' => 'MAT', 'second' => 'BIO'],
            ['name' => 'Mathematics/Computer Education', 'code' => '526240K', 'first' => 'MAT', 'second' => 'CSC'],
            ['name' => 'Mathematics/Economics', 'code' => '526241I', 'first' => 'MAT', 'second' => 'ECO'],
            ['name' => 'Primary Education Studies (DM)', 'code' => '526227E', 'first' => 'EDU', 'second' => 'EDU'],
            ['name' => 'Social Studies (DM)', 'code' => '526228C', 'first' => 'SOS', 'second' => null],
            ['name' => 'Social Studies/Economics', 'code' => '526229A', 'first' => 'SOS', 'second' => 'ECO'],
            ['name' => 'Social Studies/Political Science', 'code' => '526230D', 'first' => 'SOS', 'second' => 'POL'],
            ['name' => 'Yoruba/Social Studies', 'code' => '526234G', 'first' => 'YOR', 'second' => 'SOS'],
        ];

        foreach ($combinations as $combo) {
            Programme::create([
                'name' => $combo['name'],
                'code' => $combo['code'],
                'first_department_id' => Department::where('code', $combo['first'])->value('id'),
                'second_department_id' => Department::where('code', $combo['second'])->value('id'),
                'school_id' => Department::where('code', $combo['first'])->value('school_id'),
                'is_active' => true,
            ]);
        }
    }
}
