<?php

namespace App\Services;

use App\Models\SchoolSession;
use App\Models\SemesterSchedule;
use Carbon\Carbon;

class AcademicCalendarService
{
    public function getCurrentSession(): string
    {
        return activeSchoolSession()?->name ?? 'No active session';
    }

    public function getCurrentSessionPeriod(): string
    {
        $activeSession = activeSchoolSession();

        if (!$activeSession) {
            return 'No session details';
        }

        $semesters = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->orderBy('semester_start')
            ->get(['semester_start', 'semester_end']);

        if ($semesters->isEmpty()) {
            return 'No session details';
        }

        $start = Carbon::parse($semesters->first()->semester_start)->format('M j, Y');
        $end = Carbon::parse($semesters->last()->semester_end)->format('M j, Y');

        return "<b>{$start}</b> to <b>{$end}</b>";
    }

    public function getCurrentSemester(): string
    {
        return activeSemester()?->name ?? 'Holiday';
    }

    public function getCurrentSemesterPeriod(): string
    {
        $currentSemester = activeSemester();
        $activeSession = activeSchoolSession();

        if (!$currentSemester || !$activeSession) {
            return 'No semester details';
        }

        $schedule = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->where('semester_id', $currentSemester->id)
            ->first(['semester_start', 'semester_end']);

        if (!$schedule) {
            return 'No semester details';
        }

        $start = Carbon::parse($schedule->semester_start)->format('M j, Y');
        $end = Carbon::parse($schedule->semester_end)->format('M j, Y');

        return "<b>{$start}</b> to <b>{$end}</b>";
    }

    public function getNextSemester(): string
    {
        $activeSession = activeSchoolSession();

        if (!$activeSession) {
            return 'No upcoming semester';
        }

        $nextSemester = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->where('semester_start', '>', now())
            ->orderBy('semester_start')
            ->with('semester')
            ->first();

        if ($nextSemester) {
            return $nextSemester->semester->name;
        }

        $currentSessionEnd = $this->getSessionEndDate($activeSession->id);

        if ($currentSessionEnd) {
            $nextSession = SchoolSession::whereHas('semesterSchedules', function ($query) use ($currentSessionEnd) {
                $query->where('semester_start', '>', $currentSessionEnd);
            })->orderBy('id')->first();

            if ($nextSession) {
                return SemesterSchedule::where('school_session_id', $nextSession->id)
                    ->orderBy('semester_start')
                    ->with('semester')
                    ->first()?->semester?->name ?? 'No upcoming semester';
            }
        }

        return 'No upcoming semester';
    }

    public function getNextSemesterPeriod(): string
    {
        $activeSession = activeSchoolSession();

        if (!$activeSession) {
            return 'No semester details';
        }

        $nextSchedule = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->where('semester_start', '>', now())
            ->orderBy('semester_start')
            ->first(['semester_start', 'semester_end']);

        if ($nextSchedule) {
            return $this->formatSemesterPeriod($nextSchedule);
        }

        $currentSessionEnd = $this->getSessionEndDate($activeSession->id);

        if ($currentSessionEnd) {
            $nextSession = SchoolSession::whereHas('semesterSchedules', function ($query) use ($currentSessionEnd) {
                $query->where('semester_start', '>', $currentSessionEnd);
            })->orderBy('id')->first();

            if ($nextSession) {
                $firstSemesterNextSession = SemesterSchedule::where('school_session_id', $nextSession->id)
                    ->orderBy('semester_start')
                    ->first(['semester_start', 'semester_end']);

                return $this->formatSemesterPeriod($firstSemesterNextSession);
            }
        }

        return 'No semester details';
    }

    public function getActiveSemesterText(): string
    {
        $activeSchoolSession = activeSchoolSession();
        if (!$activeSchoolSession) {
            return 'No session';
        }

        $currentDate = now();
        $semesterSchedules = SemesterSchedule::where('school_session_id', $activeSchoolSession->id)
            ->with('semester')
            ->orderBy('semester_start', 'asc')
            ->get();

        if ($semesterSchedules->isEmpty()) {
            return 'No semester schedules';
        }

        $activeSemester = null;
        $upcomingSemester = null;

        foreach ($semesterSchedules as $semesterSchedule) {
            if ($currentDate->between($semesterSchedule->semester_start, $semesterSchedule->semester_end)) {
                $activeSemester = $semesterSchedule->semester->name;
                break;
            }

            if ($currentDate->lt($semesterSchedule->semester_start)) {
                $upcomingSemester = $semesterSchedule;
                break;
            }
        }

        // If on holiday but we know the next semester in this session
        if (is_null($activeSemester) && $upcomingSemester) {
            $semesterStart = Carbon::parse($upcomingSemester->semester_start)->format('M j Y');
            return "Holiday ({$upcomingSemester->semester->name} begins on {$semesterStart})";
        }

        // If still null, check the next session
        if (is_null($activeSemester)) {
            $currentSessionEnd = $this->getSessionEndDate($activeSchoolSession->id);

            if ($currentSessionEnd) {
                $nextSession = SchoolSession::whereHas('semesterSchedules', function ($query) use ($currentSessionEnd) {
                    $query->where('semester_start', '>', $currentSessionEnd);
                })->orderBy('id')->first();

                if ($nextSession) {
                    $firstSemesterNextSession = SemesterSchedule::where('school_session_id', $nextSession->id)
                        ->orderBy('semester_start')
                        ->with('semester')
                        ->first();

                    if ($firstSemesterNextSession) {
                        $semesterStart = Carbon::parse($firstSemesterNextSession->semester_start)->format('M j Y');
                        return "Holiday ({$firstSemesterNextSession->semester->name} begins on {$semesterStart})";
                    }
                }
            }
        }

        return $activeSemester ?? 'Holiday';
    }

    private function getSessionEndDate(int $sessionId): ?Carbon
    {
        $lastSemester = SemesterSchedule::where('school_session_id', $sessionId)
            ->orderBy('semester_end', 'desc')
            ->first(['semester_end']);

        return $lastSemester ? Carbon::parse($lastSemester->semester_end) : null;
    }

    private function formatSemesterPeriod(?SemesterSchedule $schedule): string
    {
        if (!$schedule) {
            return 'No semester details';
        }

        $start = Carbon::parse($schedule->semester_start)->format('M j, Y');
        $end = Carbon::parse($schedule->semester_end)->format('M j, Y');

        return "<b>{$start}</b> to <b>{$end}</b>";
    }
}
