<?php

namespace App\Filament\Imports;

use App\Enums\Role;
use App\Models\User;
use App\Models\Level;
use App\Models\Semester;
use App\Models\Programme;
use Carbon\CarbonInterface;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Components\Select;
use Filament\Actions\Imports\Importer;
use App\Services\CodeGenerationService;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Models\Import;

class StudentImporter extends Importer
{
    protected static ?string $model = User::class;

    protected $tenant = null;

    public function __construct(
        protected Import $import,
        protected array $columnMap,
        protected array $options,
    ) {
        parent::__construct($import, $columnMap, $options);
    }

    public function getJobRetryUntil(): ?CarbonInterface
    {
        return null;
    }

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('last_name')
                ->rules(['required', 'max:20'])
                ->label('Surname')
                ->example('Hamzat')
                ->exampleHeader('SURNAME')
                ->requiredMapping(),
            ImportColumn::make('first_name')
                ->rules(['required', 'max:20'])
                ->label('First name')
                ->example('Mikail')
                ->exampleHeader('FIRST NAME')
                ->requiredMapping(),
            ImportColumn::make('middle_name')
                ->rules(['max:20'])
                ->label('Middle name')
                ->example('Olatunji')
                ->exampleHeader('MIDDLE NAME')
                ->requiredMapping(),
            ImportColumn::make('email')
                ->rules(['required', 'max:50'])
                ->label('Email')
                ->example('<EMAIL>')
                ->exampleHeader('EMAIL')
                ->requiredMapping(),
            ImportColumn::make('matric_number')
                ->rules(['required', 'max:25'])
                ->label('Matric. no.')
                ->example('RACOED/2024/POL-SOS/099')
                ->exampleHeader('MATRICULATION NUMBER')
                ->requiredMapping(),
            ImportColumn::make('phone')
                ->rules(['required', 'max:11'])
                ->label('Phone')
                ->example('07042366520')
                ->exampleHeader('PHONE')
                ->requiredMapping(),
            ImportColumn::make('password')
                ->rules(['max:10'])
                ->label('Password')
                ->example('12345678')
                ->exampleHeader('PASSWORD')
                ->requiredMappingForNewRecordsOnly(),
        ];
    }

    public static function getOptionsFormComponents(): array
    {
        return [
            Select::make('school_session_id')
                ->required()
                ->label('Session')
                ->native(false)
                ->options(function () {
                    return SchoolSession::query()
                        ->orderBy('name', 'desc')
                        ->pluck('name', 'id');
                }),
            Select::make('semester_id')
                ->required()
                ->label('Semester')
                ->native(false)
                ->options(function () {
                    return Semester::query()
                        ->orderBy('name')
                        ->pluck('name', 'id');
                }),
            Select::make('level_id')
                ->required()
                ->label('Level')
                ->native(false)
                ->options(function () {
                    return Level::query()
                        ->orderBy('name')
                        ->pluck('name', 'id');
                }),
            Select::make('programme_id')
                ->required()
                ->label('Programme')
                ->native(false)
                ->searchable()
                ->options(function () {
                    return Programme::query()
                        ->orderBy('name')
                        ->pluck('name', 'id');
                })
        ];
    }

    public function resolveRecord(): ?User
    {
        return User::firstOrNew([
            'matric_number' => $this->data['matric_number'],
        ]);
    }

    protected function beforeSave(): void
    {
        // Set fixed values for new users
        if (! $this->record->exists) {
            $this->record->role = Role::STUDENT;
        }

        // Always hash password if present
        if (!empty($this->data['password'])) {
            $this->record->password = Hash::make($this->data['password']);
        }

        // Ensure phone starts with 0
        if (!empty($this->data['phone']) && !str_starts_with($this->data['phone'], '0')) {
            $this->record->phone = '0' . ltrim($this->data['phone'], '0');
        }
    }

    protected function afterSave(): void
    {
        if (! $this->record->wasRecentlyCreated) {
            return;
        }

        $this->record->registrations()->create([
            'school_session_id' => $this->options['school_session_id'],
            'semester_id' => $this->options['semester_id'],
            'level_id' => $this->options['level_id'],
            'programme_id' => $this->options['programme_id'],
            'is_active' => true,
        ]);

        $this->record->application()->create([
            'number' => (new CodeGenerationService())->generateApplicationNumber(),
            'school_session_id' => $this->options['school_session_id'],
            'programme_id' => $this->options['programme_id'],
            'admission_status' => AdmissionStatus::APPROVED,
            // 'admission_date' => now(),
        ]);
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your student import is complete and ' . number_format($import->successful_rows) . ' ' . str('student')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('student')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
