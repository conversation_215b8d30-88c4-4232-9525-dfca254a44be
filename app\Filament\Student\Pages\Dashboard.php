<?php

namespace App\Filament\Student\Pages;

use App\Enums\Role;
use App\Enums\AdmissionStatus;
use Illuminate\Support\Facades\Auth;

class Dashboard extends \Filament\Pages\Dashboard
{
    protected static string $routePath = 'home';
    protected static ?string $title = 'Home';

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }
}
