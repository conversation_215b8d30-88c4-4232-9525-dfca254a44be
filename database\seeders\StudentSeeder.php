<?php

namespace Database\Seeders;

use App\Enums\AddressState;
use App\Enums\Role;
use App\Enums\Title;
use App\Models\User;
use App\Models\Level;
use App\Models\Invoice;
use App\Enums\ExamBoard;
use App\Models\Guardian;
use App\Models\Semester;
use App\Enums\FeeType;
use App\Models\Application;
use App\Models\Transaction;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentMethod;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use App\Enums\ScreeningStatus;
use App\Enums\TransactionType;
use Illuminate\Database\Seeder;
use App\Enums\TransactionStatus;
use App\Settings\PortalSettings;
use Illuminate\Support\Facades\Hash;
use App\Services\CodeGenerationService;

class StudentSeeder extends Seeder
{
    public function run()
    {
        $students = [
            [
                'last_name' => '<PERSON>laniyan',
                'first_name' => '<PERSON>seni',
                'middle_name' => 'Taiye',
                'email' => '<EMAIL>',
                'phone' => '08135501692',
                'jamb' => '12345678FF',
            ],
            [
                'last_name' => 'Adebayo',
                'first_name' => 'Tosin',
                'middle_name' => 'Kehinde',
                'email' => '<EMAIL>',
                'phone' => '08130001111',
                'jamb' => '12345678AA',
            ],
            [
                'last_name' => 'Ajayi',
                'first_name' => 'Sade',
                'middle_name' => 'Funmilayo',
                'email' => '<EMAIL>',
                'phone' => '08130002222',
                'jamb' => '12345678BB',
            ],
            [
                'last_name' => 'Ibrahim',
                'first_name' => 'Kabir',
                'middle_name' => 'Olamide',
                'email' => '<EMAIL>',
                'phone' => '08130003333',
                'jamb' => '12345678CC',
            ],
            [
                'last_name' => 'Okoro',
                'first_name' => 'Chinwe',
                'middle_name' => 'Amaka',
                'email' => '<EMAIL>',
                'phone' => '08130004444',
                'jamb' => '12345678DD',
            ],
        ];

        $programmeIds = [1, 2, 3];
        $admissionStatuses = [
            AdmissionStatus::APPROVED,
            AdmissionStatus::PENDING,
            AdmissionStatus::DENIED,
        ];

        foreach ($students as $index => $student) {
            $programmeId = $programmeIds[$index % count($programmeIds)];
            $admissionStatus = $admissionStatuses[$index % count($admissionStatuses)];

            $portalFee = app(PortalSettings::class)->portal_fee;

            $schoolSession = SchoolSession::first();
            $semester = Semester::first();
            $level = Level::first();

            $user = User::create([
                'title' => Title::MR,
                'last_name' => $student['last_name'],
                'first_name' => $student['first_name'],
                'middle_name' => $student['middle_name'],
                'phone' => $student['phone'],
                'email' => $student['email'],
                'role' => Role::STUDENT,
                'address_line' => 'Osolo Street',
                'address_town' => 'Obaagun',
                'address_state' => AddressState::OSUN,
                'password' => Hash::make('12345678'),
                'date_of_birth' => '2000-01-01',
                'gender' => 'male',
                'marital_status' => 'single',
                'religion' => 'christianity',
                'nationality' => 'nigerian',
                'state_id' => 1,
                'local_government_area_id' => 1,
            ]);

            Guardian::create([
                'user_id' => $user->id,
                'title' => Title::MR,
                'last_name' => 'Ade',
                'first_name' => $student['last_name'],
                'phone' => $student['phone'],
                'relationship' => 'father',
                'occupation' => 'teacher',
            ]);

            Application::create([
                'user_id' => $user->id,
                'number' => (new CodeGenerationService())->generateApplicationNumber(),
                'school_session_id' => $schoolSession->id,
                'programme_id' => $programmeId,
                'secondary_school_attended' => 'Akinorun High School Osogbo, Osun State.',
                'secondary_school_graduation_year' => 2024,
                'jamb_registration_number' => $student['jamb'],
                'exam_board' => ExamBoard::WAEC,
                'exam_result' => [
                    ['subject' => 'accounting', 'grade' => 'b2'],
                    ['subject' => 'book_keeping', 'grade' => 'b3'],
                    ['subject' => 'agricultural_science', 'grade' => 'b2'],
                    ['subject' => 'christian_religious_studies', 'grade' => 'b3'],
                    ['subject' => 'arabic', 'grade' => 'b2'],
                    ['subject' => 'data_processing', 'grade' => 'b2'],
                    ['subject' => 'biology', 'grade' => 'b2'],
                    ['subject' => 'computer_studies', 'grade' => 'b3'],
                    ['subject' => 'chemistry', 'grade' => 'b3'],
                ],
                'is_declared' => true,
                'screening_score' => 90,
                'screening_status' => ScreeningStatus::PASSED,
                'admission_status' => $admissionStatus,
                'admission_date' => now(),
            ]);

            Registration::create([
                'user_id' => $user->id,
                'school_session_id' => $schoolSession->id,
                'level_id' => $level->id,
                'semester_id' => $semester->id,
                'programme_id' => $programmeId,
                'is_active' => true,
            ]);

            $user->update([
                'matric_number' => (new CodeGenerationService())->generateMatriculationNumber($schoolSession->id, $programmeId),
            ]);

            $invoice = Invoice::create([
                'user_id' => $user->id,
                'payable_id' => $user->activeRegistration->id,
                'payable_type' => Registration::class,
                'number' => (new CodeGenerationService())->generateInvoiceNumber(),
                'reference' => (new CodeGenerationService())->generateReference(),
                'description' => [
                    [
                        'item' => "Portal Access",
                        'amount' => $portalFee,
                    ]
                ],
                'total_amount' =>  $portalFee,
                'fee_type' => FeeType::PORTAL,
                'invoice_status' => InvoiceStatus::PAID,
                'paid_at' => now(),
            ]);

            Transaction::create([
                'invoice_id' => $invoice->id,
                'number' => (new CodeGenerationService())->generateTransactionNumber(),
                'amount' => $invoice->total_amount,
                'transaction_status' => TransactionStatus::SUCCESS,
                'transaction_type' => TransactionType::CREDIT,
                'payment_method' => PaymentMethod::ONLINE,
                'payment_channel' => 'card',
            ]);
        }
    }
}
