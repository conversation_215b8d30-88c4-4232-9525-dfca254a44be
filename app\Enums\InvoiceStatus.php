<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum InvoiceStatus: int implements HasLabel, HasColor
{
    case INITIATED = 0;
    case UNPAID = 1;
    case PARTIAL = 2;
    case PAID = 3;
    case OVERPAID = 4;
    case CANCELLED = 5;

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::INITIATED => 'Initiated',
            self::UNPAID => 'Unpaid',
            self::PARTIAL => 'Partial',
            self::PAID => 'Paid',
            self::OVERPAID => 'Overpaid',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get the color for the type
     */
    public function getColor(): string
    {
        return match ($this) {
            self::INITIATED => 'gray',
            self::UNPAID => 'gray',
            self::PAID => 'success',
            self::PARTIAL => 'info',
            self::OVERPAID => 'warning',
            self::CANCELLED => 'danger',
        };
    }

    public function getTailwindColor(): string
    {
        return match ($this) {
            self::INITIATED => 'gray-600',
            self::UNPAID => 'gray-600',
            self::PARTIAL => 'blue-600',
            self::PAID => 'green-600',
            self::OVERPAID => 'yellow-600',
            self::CANCELLED => 'red-600',
        };
    }
}
