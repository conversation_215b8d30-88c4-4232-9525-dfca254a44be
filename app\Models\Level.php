<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Level extends Model
{
    use SoftDeletes;

    /**
     * Get all of the registrations for the Level
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    // public function registrations(): HasMany
    // {
    //     // return $this->hasMany(Registration::class);
    // }

    /**
     * Get all of the courses for the Level
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function courses(): HasM<PERSON>
    {
        return $this->hasMany(Course::class);
    }
}
