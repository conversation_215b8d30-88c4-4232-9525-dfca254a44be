 @php
    use App\Filament\Staff\Resources\OverviewResource;
    use App\Models\Assessment; 

    $student = $getRecord();
    $courseData = OverviewResource::getCourseData($this, $student);
       
    $assessmentNames = Assessment::pluck('max_score', 'name')->all();

        // Semester calculations
        $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($this, $student);
        $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePoint($this, $student);
        $semesterGradePointAverage = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit, 2) : null;
        $semesterGradeRemark = OverviewResource::getRemarkFromGradePointAverage($semesterGradePointAverage);
        $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCourses($this, $student);

        // Calculate Cumulative Summary
        $cumulativeTotalCreditUnit = OverviewResource::getCumulativeTotalCreditUnit($this, $student);
        $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePoint($this, $student);
        $cumulativeGradePointAverage = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint / $cumulativeTotalCreditUnit, 2) : null;
        $cumulativeGradeRemark = OverviewResource::getRemarkFromGradePointAverage($cumulativeGradePointAverage);
        $cumulativeOutstandingCourses = OverviewResource::getCumulativeOutstandingCourses($this, $student);     

@endphp

<div class="flex text-xs gap-1">
    {{-- Summary Table --}}
    <div class="w-1/3 space-y-3">
        <table class="w-full text-xs">
            <thead class="bg-gray-100">
                <tr class="text-center">
                    <th colspan="5" class="p-1 border border-gray-300">Semester summary</th>
                </tr>
                <tr class="text-left">
                    <th class="p-1 border border-gray-300" x-tooltip.raw="Total Credit Unit">TCU</th>
                    <th class="p-1 border border-gray-300" x-tooltip.raw="Total Grade Point">TGP</th>
                    <th class="p-1 border border-gray-300" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="p-1 border border-gray-300">Remark</th>
                    <th class="p-1 border border-gray-300" x-tooltip.raw="Carry-over Courses">Outstanding</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="p-1 border border-gray-300">{{ $semesterTotalCreditUnit ?? '-' }}</td>
                    <td class="p-1 border border-gray-300">{{ $semesterTotalGradePoint ?? '-' }}</td>
                    <td class="p-1 border border-gray-300">{{ $semesterGradePointAverage ?? '-' }}</td>
                    <td class="p-1 border border-gray-300">{{ $semesterGradeRemark?->remark ?? '-' }}</td>
                    <td class="p-1 border border-gray-300 whitespace-nowrap">
                        {{ $semesterOutstandingCourses->implode(', ') ?: 'NIL' }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    {{-- Course Table --}}
    <div class="w-2/3 overflow-x-auto">
        <table class="min-w-full text-xs">
            <thead class="bg-gray-100">
                <tr>
                    @foreach ($courseData as $data)
                        <th class="border border-gray-300 px-2 py-1 text-center" colspan="4" x-tooltip.raw="{{ $data['title'] }}">
                            {{ $data['code'] }}
                            <span class="text-gray-400">{{ $data['credit'] }}{{ $data['status'] }}</span>
                        </th>
                    @endforeach
                </tr>
                <tr>
                    @foreach ($courseData as $data)
                        <th class="border border-gray-300 px-2 py-1 text-center" x-tooltip.raw="Score">S</th>
                        <th class="border border-gray-300 px-2 py-1 text-center" x-tooltip.raw="Grade">G</th>
                        <th class="border border-gray-300 px-2 py-1 text-center" x-tooltip.raw="Point">P</th>
                        <th class="border border-gray-300 px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                <tr>
                    @foreach ($courseData as $data)
                        @php $failed = ($data['total_score'] ?? -1) <= OverviewResource::getFailedScore(); @endphp
                        <td class="border border-gray-300 px-2 py-1 text-center text-{{ $failed ? 'red-500' : 'gray-900' }}">
                            {{ $data['total_score'] ?? '-' }}
                        </td>
                        <td class="border border-gray-300 px-2 py-1 text-center text-{{ $failed ? 'red-500' : 'gray-900' }}">
                            {{ $data['grade'] ?? '-' }}
                        </td>
                        <td class="border border-gray-300 px-2 py-1 text-center">{{ $data['point'] ?? '-' }}</td>
                        <td class="border border-gray-300 px-2 py-1 text-center">{{ $data['grade_point'] ?? '-' }}</td>
                    @endforeach
                </tr>
            </tbody>
        </table>
    </div>
</div>



