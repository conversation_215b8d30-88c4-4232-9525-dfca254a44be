<?php

namespace App\Filament\Staff\Resources\AssessmentResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\AssessmentResource;

class ManageAssessments extends ManageRecords
{
    protected static string $resource = AssessmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->successNotification(function () {
            //         return Notification::make()
            //             ->success()
            //             ->title('Assessment Created')
            //             ->body('The assessment has been created successfully');
            //     }),
        ];
    }
}
