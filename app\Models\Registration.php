<?php

namespace App\Models;

use App\Enums\FeeType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Registration extends Model
{

    protected $casts = [
        'is_active' => 'boolean',
        'is_graduated' => 'boolean',
        'is_withdrawn' => 'boolean',
    ];


    /**
     * Get the period for the Fee
     *
     * @return array
     */
    public function getPeriodAttribute()
    {
        return [
            'level' => 'Level: ' . $this->level->name,
            'semester' => 'Semester: ' . $this->semester->name,
            'session' => 'Session: ' . $this->schoolSession->name,
        ];
    }

    /**
     * Get the user that owns the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the school session that owns the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function schoolSession(): BelongsTo
    {
        return $this->belongsTo(SchoolSession::class);
    }

    /**
     * Get the semester that owns the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    /**
     * Get the level that owns the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(Level::class);
    }

    /**
     * Get the programme that owns the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function programme(): BelongsTo
    {
        return $this->belongsTo(Programme::class);
    }

    /**
     * Get the portal invoice for the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne
     */
    public function portalInvoice(): MorphOne
    {
        return $this->morphOne(Invoice::class, 'payable')
            ->where('fee_type', FeeType::PORTAL);
    }

    /**
     * Get the invoices for the Registration
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function invoices(): MorphMany
    {
        return $this->morphMany(Invoice::class, 'payable');
    }
}
