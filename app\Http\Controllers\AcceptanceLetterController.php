<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Models\User;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;

class AcceptanceLetterController extends Controller
{

    public function print(User $student)
    {
        return view('filament.documents.acceptance-letter', $this->getAcceptanceLetterData($student));
    }

    public function download(User $student)
    {
        return Pdf::view('filament.documents.acceptance-letter', $this->getAcceptanceLetterData($student))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name('Acceptance letter - ' . $student->name . '.pdf')
            ->download();
    }

    private function getAcceptanceLetterData(User $student)
    {
        $collegeSettings = app(CollegeSettings::class);

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
        ];
    }
}
