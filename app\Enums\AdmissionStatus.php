<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum AdmissionStatus: int implements HasLabel, HasColor
{

    case PENDING = 0;
    case APPROVED = 1;
    case DENIED = 2;

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::APPROVED => 'Approved',
            self::DENIED => 'Denied',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::PENDING => 'gray',
            self::APPROVED => 'success',
            self::DENIED => 'danger',
        };
    }
}
