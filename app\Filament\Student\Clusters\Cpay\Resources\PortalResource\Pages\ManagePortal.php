<?php

namespace App\Filament\Student\Clusters\Cpay\Resources\PortalResource\Pages;

use App\Models\Invoice;
use Livewire\Attributes\On;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Student\Clusters\Cpay\Resources\PortalResource;

class ManagePortal extends ManageRecords
{
    protected static string $resource = PortalResource::class;
    protected $listeners = [
        'refresh-table' => '$refresh',
    ];

    #[On('payment-success')]
    public function paymentSuccess($invoiceId)
    {
        $invoice = Invoice::withTrashed()->find($invoiceId);

        if ($invoice) {
            $invoice->update(['payment_success' => true]);

            if ($invoice->trashed()) {
                $invoice->restore();
            }
        }

        Notification::make()
            ->success()
            ->title('Payment Completed')
            ->body('The payment was processed successfully. Thank you.')
            ->send();

        $this->dispatch('refresh-table');
    }

    #[On('payment-canceled')]
    public function paymentCanceled($invoiceId)
    {
        $invoice = Invoice::find($invoiceId);

        if ($invoice) {
            $invoice->delete();
        }

        Notification::make()
            ->danger()
            ->title('Payment Canceled')
            ->body('The payment must be completed to process your fees. Please try again or contact the school admin for assistance.')
            ->send();
    }
}
