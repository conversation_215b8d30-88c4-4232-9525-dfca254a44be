<?php

namespace App\Filament\Staff\Pages\Auth;

use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Filament\Student\Pages\Dashboard;
use Filament\Forms\Components\Actions\Action;

class ChangePassword extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static string $view = 'filament.pages.change-password';
    protected static ?string $title = 'Change Password';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $layout = 'filament-panels::components.layout.simple';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('current_password')
                    ->label('Current Password')
                    ->password()
                    ->required()
                    ->rules(['current_password'])
                    ->validationMessages([
                        'current_password' => 'Your current password is incorrect.',
                    ]),

                TextInput::make('password')
                    ->label('New Password')
                    ->password()
                    ->revealable(filament()->arePasswordsRevealable())
                    ->autocomplete('new-password')
                    ->live(debounce: 500)
                    ->required()
                    ->minLength(4),

                TextInput::make('password_confirmation')
                    ->label('Confirm New Password')
                    ->password()
                    ->revealable(filament()->arePasswordsRevealable())
                    ->required()
                    ->same('password')
                    ->visible(fn(Get $get): bool => filled($get('password'))),
            ])->columns(3)
            ->statePath('data');
    }

    public function changePassword(): void
    {
        $data = $this->form->getState();

        $user = Auth::user();

        if (!Hash::check($data['current_password'], $user->password)) {
            Notification::make()
                ->title('Error')
                ->body('Your current password is incorrect')
                ->danger()
                ->send();
            return;
        }

        if (request()->hasSession() && array_key_exists('password', $data)) {
            request()->session()->put([
                'password_hash_' . filament()->getAuthGuard() => $data['password'],
            ]);
        }

        $user->update([
            'password' => Hash::make($data['password'])
        ]);

        Notification::make()
            ->title('Password Updated')
            ->body('Password changed successfully')
            ->success()
            ->send();

        redirect($this->getRedirectUrl());
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('changePassword')
                ->label('Change Password')
                ->submit('changePassword'),
        ];
    }

    protected function getRedirectUrl(): ?string
    {
        return Dashboard::getUrl(['panel' => 'staff']);
    }
}
