<?php

namespace App\Models;

use App\Enums\ExamBoard;
use App\Enums\AdmissionStatus;
use App\Enums\ScreeningStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Application extends Model
{
    protected $casts = [
        'exam_result' => 'array',
        'exam_board' => ExamBoard::class,
        'is_declared' => 'boolean',
        'admission_status' => AdmissionStatus::class,
        'screening_status' => ScreeningStatus::class,
        'admission_date' => 'datetime'
    ];

    /**
     * Get the user that owns the Application
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the school session that owns the Application
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function schoolSession(): BelongsTo
    {
        return $this->belongsTo(SchoolSession::class);
    }

    /**
     * Get the programme that owns the Application
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function programme(): BelongsTo
    {
        return $this->belongsTo(Programme::class);
    }
}
