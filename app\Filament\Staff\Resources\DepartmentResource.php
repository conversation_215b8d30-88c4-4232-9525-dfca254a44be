<?php

namespace App\Filament\Staff\Resources;


use Filament\Tables;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use App\Filament\Staff\Resources\DepartmentResource\Pages;

class DepartmentResource extends Resource
{
    protected static ?string $model = Department::class;
    protected static ?int $navigationSort = 5;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of departments';

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(25)
                    ->label('Department')
                    ->placeholder('Computer Science')
                    ->unique(ignoreRecord: true),
                TextInput::make('code')
                    ->required()
                    ->maxLength(10)
                    ->label('Code')
                    ->placeholder('CSC')
                    ->unique(ignoreRecord: true),
                Select::make('school_id')
                    ->required()
                    ->label('School')
                    ->native(false)
                    ->relationship('school', 'name')
                    ->placeholder('Select a school'),
                Checkbox::make('is_edu')
                    ->label('Education')
                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Must check this box if it is an Education department.')
                    ->inline(false),
                Checkbox::make('is_gse')
                    ->label('General Studies Education')
                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Must check this box if it is a General Studies Education department.')
                    ->inline(false),
            ])->columns(5);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('name')
            ->emptyStateHeading('No Departments Yet')
            ->emptyStateDescription('Once you create your first department, it will appear here.')
            ->description('The list of departments created for efficient academic management.')

            ->columns([
                TextColumn::make('#')
                    ->width('2rem')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->width('2rem'),
                TextColumn::make('school.name')
                    ->label('School'),
            ])
            ->filters([
                //
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Department updated')
                                ->body('The department has been updated successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Department?')
                        ->modalDescription('Are you sure you want to delete this department?')
                        ->modalSubmitActionLabel('Yes, delete it')
                        ->modalCancelActionLabel('No, keep it')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['registrations'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this department because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Department Deleted')
                                ->body('The department has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Department actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageDepartments::route('/'),
        ];
    }
}
