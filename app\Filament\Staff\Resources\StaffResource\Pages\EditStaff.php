<?php

namespace App\Filament\Staff\Resources\StaffResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Staff\Resources\StaffResource;

class EditStaff extends EditRecord
{
    protected static string $resource = StaffResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn() => main_staff_access())
                ->successNotification(
                    Notification::make()
                        ->success()
                        ->title('Admin Deleted')
                        ->body('The admin has been deleted successfully.'),
                ),
        ];
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Staff Updated')
            ->body("The staff's details has been updated successfully.");
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
