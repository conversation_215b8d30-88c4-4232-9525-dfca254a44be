<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    public function run()
    {
        $departments = [
            1 => [
                ['name' => 'Economics', 'code' => 'ECO'],
                ['name' => 'Islamic Studies', 'code' => 'ISS'],
                ['name' => 'Political Science', 'code' => 'POL'],
                ['name' => 'Social Studies', 'code' => 'SOS'],
            ],
            2 => [
                ['name' => 'Education', 'code' => 'EDU', 'is_edu' => true],
            ],
            3 => [
                ['name' => 'General Studies Education', 'code' => 'GSE', 'is_gse' => true],
            ],
            4 => [
                ['name' => 'Arabic', 'code' => 'ARB'],
                ['name' => 'English', 'code' => 'ENG'],
                ['name' => 'French', 'code' => 'FRE'],
                ['name' => 'Yoruba', 'code' => 'YOR'],
            ],
            5 => [
                ['name' => 'Biology', 'code' => 'BIO'],
                ['name' => 'Chemistry', 'code' => 'CHE'],
                ['name' => 'Computer Science', 'code' => 'CSC'],
                ['name' => 'Mathematics', 'code' => 'MAT'],
                ['name' => 'Physics', 'code' => 'PHY'],
            ],
            6 => [
                ['name' => 'Business Education', 'code' => 'BED'],
            ],
        ];


        foreach ($departments as $schoolId => $items) {
            foreach ($items as $item) {
                Department::create([
                    'name' => $item['name'],
                    'code' => $item['code'],
                    'school_id' => $schoolId,
                    'is_edu' => $item['is_edu'] ?? false,
                    'is_gse' => $item['is_gse'] ?? false,
                ]);
            }
        }
    }
}
