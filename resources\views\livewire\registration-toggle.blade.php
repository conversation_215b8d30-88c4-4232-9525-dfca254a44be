<div 
    class="relative inline-flex h-6 w-11 shrink-0 rounded-full border-2 border-transparent outline-none transition-colors duration-200 ease-in-out {{ $isActive ? 'bg-red-800' : 'bg-gray-200 dark:bg-gray-700' }} {{ $isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' }}"
    wire:click="toggle"
>
    <span class="pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {{ $isActive ? 'translate-x-5 rtl:-translate-x-5' : 'translate-x-0' }}">
    </span>
</div>