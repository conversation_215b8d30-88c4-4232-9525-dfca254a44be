<?php

namespace App\Settings;

use App\Casts\SettingsMoneyCast;
use <PERSON><PERSON>\LaravelSettings\Settings;

class PortalSettings extends Settings
{

    public float $portal_fee;

    public static function group(): string
    {
        return 'portal';
    }

    public static function casts(): array
    {
        return [
            'portal_fee' => SettingsMoneyCast::class,
        ];
    }

    public static function default(): array
    {
        return [
            'portal_fee' => 300000,
        ];
    }
}
