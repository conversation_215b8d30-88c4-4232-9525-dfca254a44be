<?php

namespace App\Filament\Student\Resources\ResultResource\Pages;

use Filament\Resources\Pages\ManageRecords;
use App\Filament\Student\Resources\ResultResource;

class ManageResults extends ManageRecords
{
    protected static string $resource = ResultResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
