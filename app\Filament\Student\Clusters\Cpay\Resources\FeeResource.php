<?php

namespace App\Filament\Student\Clusters\Cpay\Resources;

use App\Enums\Role;
use App\Models\Fee;
use Filament\Tables;
use App\Models\Semester;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use App\Services\MoneyService;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\Alignment;
use App\Filament\Student\Clusters\Cpay;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Pages\SubNavigationPosition;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Student\Clusters\Cpay\Resources\FeeResource\Pages;
use App\Filament\Student\Clusters\Cpay\Resources\FeeResource\RelationManagers;

class FeeResource extends Resource
{
    protected static ?string $model = Fee::class;
    protected static ?int $navigationSort = 2;
    protected static ?string $cluster = Cpay::class;
    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['invoices', 'feeCategory'])
            ->where('is_scheduled', true);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        $student = Auth::user();

        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('fees.created_at', 'desc')
            ->emptyStateHeading('No Fees Yet')
            ->emptyStateDescription('Once fees are scheduled for you, it will appear here.')
            ->description('List of all college fees to help you manage and track payments easily.')
            ->filtersLayout(FiltersLayout::AboveContent)

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('feeCategory.name')
                    ->label('Fee'),
                TextColumn::make('feeCategory.fee_type')
                    ->label('Fee type'),
                TextColumn::make('description')
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(
                        fn($record) =>
                        is_array($record->description)
                            ? array_map(fn($item) => $item['item'] . ' - ₦' . number_format($item['amount']), $record->description)
                            : [$record->description]
                    ),
                TextColumn::make('period')
                    ->listWithLineBreaks()
                    ->bulleted(),
                ColumnGroup::make('Summary', [
                    TextColumn::make('total_amount')
                        ->prefix('₦')
                        ->formatStateUsing(fn($state) => number_format($state)),
                    TextColumn::make('paid_amount')
                        ->placeholder('NIL')
                        ->prefix('₦')
                        ->formatStateUsing(fn($state) => number_format(app(MoneyService::class)->toNaira($state))),
                    TextColumn::make('due_amount')
                        ->placeholder('NIL')
                        ->prefix('₦')
                        ->formatStateUsing(fn($state) => number_format(app(MoneyService::class)->toNaira($state))),
                ])
                    ->alignment(Alignment::Center),
            ])
            ->filters([
                SelectFilter::make('school_session_id')
                    ->label('Session')
                    ->options(function () use ($student) {
                        return SchoolSession::whereIn('id', function ($query) use ($student) {
                            $query->select('school_session_id')
                                ->from('registrations')
                                ->where('user_id', $student->id)
                                ->distinct();
                        })
                            ->orderBy('name', 'desc')
                            ->pluck('name', 'id');
                    })
                    ->native(false)
                    ->default($student->activeSchoolSession->id),
                SelectFilter::make('semester_id')
                    ->label('Semester')
                    ->options(function () use ($student) {
                        return Semester::whereIn('id', function ($query) use ($student) {
                            $query->select('semester_id')
                                ->from('registrations')
                                ->where('user_id', $student->id)
                                ->distinct();
                        })
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->native(false)
                    ->default($student->activeSemester->id),
            ], layout: FiltersLayout::AboveContent)


            // ->actions([
            //     Tables\Actions\Action::make('pay')
            //         ->visible(fn($record) => (int) $record->paid < $record->amount)
            //         ->label('PAY')
            //         ->icon('heroicon-o-credit-card')
            //         ->button()
            //         ->tooltip('Pay fee')
            //         ->modalHeading('Make Payment?')
            //         // ->modalDescription(fn($record) => new HtmlString('You are making payment for <b>' . ($record->feeCategory->name) . '</b> in the amount of ₦<b>' . number_format($record->total_amount) . '</b>. Would you like to proceed?'))
            //         ->modalDescription(fn($record) => new HtmlString(
            //             'You are making the payment of <b>' . $record->feeCategoryName . '</b>. Installments are available. Choose your payment amount and proceed.'
            //         ))
            //         ->modalSubmitActionLabel('Proceed to Pay')
            //         ->modalContent(fn($record): View => view(
            //             'filament.payment-table',
            //             [
            //                 'record' => $record,
            //                 'filteredInvoices' => $record->invoices()
            //                     ->where('fee_id', $record->id)
            //                     ->where('invoice_status', InvoiceStatus::PAID)
            //                     ->get(),
            //             ],
            //         ))
            //         ->form([
            //             FormSection::make('')
            //                 ->description('')
            //                 ->schema([
            //                     Radio::make('method')
            //                         ->required()
            //                         ->options([
            //                             PaymentMethod::ONLINE->value => PaymentMethod::ONLINE->getLabel(),
            //                         ])
            //                         ->default(PaymentMethod::ONLINE->value),
            //                     Select::make('amount')
            //                         ->required()
            //                         ->native(false)
            //                         ->options(function ($record) {

            //                             $total =  $record->amount;
            //                             $paid =  $record->paid;
            //                             $due =  $record->due;

            //                             $installments = [
            //                                 '0.25' => '25%',
            //                                 '0.50' => '50%',
            //                                 '0.75' => '75%',
            //                                 '1.00' => '100%',
            //                             ];

            //                             $options = [];
            //                             foreach ($installments as $ratio => $label) {
            //                                 $installmentAmount = $total * $ratio;
            //                                 $remainingInstallment = $installmentAmount - $paid;

            //                                 if ($remainingInstallment <= $due && $remainingInstallment > 0) {
            //                                     $options[] = [
            //                                         'label' => '₦' . number_format((new MoneyCast())->get(null, 'amount', $remainingInstallment, [])) . " ({$label})",
            //                                         'value' => $remainingInstallment,
            //                                     ];
            //                                 }
            //                             }

            //                             return collect($options)->pluck('label', 'value')->toArray();
            //                         })
            //                 ])
            //                 ->columns(4),
            //         ])
            //         ->action(function ($data, $record, $livewire) {
            //             $livewire->mountAction('confirmPaymentAction', [
            //                 'paymentData' => $data,
            //                 'tableRecord' => array_merge($record->toArray(), [
            //                     'user' => Auth::user(),
            //                 ]),
            //             ]);
            //         }),

            //     Tables\Actions\Action::make('paid')
            //         ->visible(fn($record) => (int) $record->paid >= $record->amount)
            //         ->label('PAID')
            //         // ->disabled()
            //         ->icon('heroicon-o-check-circle')
            //         ->color('success')
            //         ->button()
            //         ->tooltip('Fee paid, view payment invoice.')
            //         ->modalHeading('Payment details')
            //         ->modalContent(fn($record): View => view(
            //             'filament.payment-table',
            //             [
            //                 'record' => $record,
            //                 'filteredInvoices' => $record->invoices()
            //                     ->where('fee_id', $record->id)
            //                     ->where('invoice_status', InvoiceStatus::PAID->value)
            //                     ->get(),
            //             ],
            //         ))
            //         ->modalSubmitAction(false),
            // ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageFees::route('/'),
        ];
    }
}
