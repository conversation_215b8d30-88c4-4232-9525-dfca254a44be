<?php

namespace App\Filament\Staff\Resources\GradeResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\GradeResource;

class ManageGrades extends ManageRecords
{
    protected static string $resource = GradeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->successNotification(function () {
            //         return Notification::make()
            //             ->success()
            //             ->title('Grade Created')
            //             ->body('The grade has been created successfully');
            //     }),
        ];
    }
}
