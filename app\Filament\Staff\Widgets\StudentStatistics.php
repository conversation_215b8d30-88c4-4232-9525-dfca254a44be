<?php

namespace App\Filament\Staff\Widgets;

use App\Enums\Role;
use App\Models\User;
use App\Enums\AdmissionStatus;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class StudentStatistics extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';
    protected ?string $heading = 'Student Statistics';
    protected ?string $description = 'View detailed statistics about students including admissions and registrations.';
    protected static ?int $sort = 1;

    public static function canView(): bool
    {
        $user = Auth::user();

        return $user && in_array($user->role, main_staff_roles());
    }

    protected function getStats(): array
    {
        $activeSchoolSession = activeSchoolSession();

        return [
            Stat::make('Students Population', $this->getTotalStudents())
                ->description('Total number of admitted students.')
                ->descriptionIcon('heroicon-m-user-group', IconPosition::Before)
                ->color('info'),
            Stat::make('Active Registrations', $this->getActiveRegistrations())
                ->description(new HtmlString('Students registered for <b>' . ($activeSchoolSession?->name ?? 'no active session') . '</b>.'))
                ->descriptionIcon('heroicon-m-academic-cap', IconPosition::Before)
                ->color('info'),
            Stat::make('Pending Admissions', $this->getPendingAdmissions())
                ->description('Students awaiting admission approval.')
                ->descriptionIcon('heroicon-m-user-group', IconPosition::Before)
                ->color('info'),
        ];
    }
    private function getTotalStudents()
    {
        return User::where('role', Role::STUDENT)
            ->whereHas(
                'application',
                fn($query) =>
                $query->where('admission_status', AdmissionStatus::APPROVED)
            )
            ->count();
    }

    private function getActiveRegistrations()
    {
        return User::where('role', Role::STUDENT)
            ->whereHas('activeRegistration')
            ->count();
    }


    private function getPendingAdmissions()
    {
        return User::where('role', Role::STUDENT)
            ->whereHas(
                'application',
                fn($query) =>
                $query->where('admission_status', AdmissionStatus::PENDING)
            )
            ->count();
    }
}
