<?php

namespace App\Models;

use App\Enums\CourseStatus;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Course extends Model
{
    public function code(): Attribute
    {
        return Attribute::get(fn($value) => Str::upper($value));
    }

    public function title(): Attribute
    {
        return Attribute::get(function ($value) {
            $title = Str::apa($value);
            return str_replace(['Iv', 'Iii', 'Ii', 'Crs', 'Irs'], ['IV', 'III', 'II', 'CRS', 'IRS'], $title);
        });
    }

    protected $casts = [
        'course_status' => CourseStatus::class,
    ];

    /**
     * Get the department that owns the Course
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the semester that owns the Course
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    /**
     * Get the level that owns the Course
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(Level::class);
    }
}
