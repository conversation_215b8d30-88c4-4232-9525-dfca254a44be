<?php

use App\Enums\Role;
use App\Models\Semester;
use App\Models\SchoolSession;
use App\Models\SemesterSchedule;
use Illuminate\Support\Facades\Auth;

if (!function_exists('activeSchoolSession')) {
    function activeSchoolSession(): ?SchoolSession
    {
        return SchoolSession::where('is_active', true)
            ->first();
    }
}

if (!function_exists('activeSemester')) {
    function activeSemester(): ?Semester
    {
        if (!($session = activeSchoolSession())) {
            return null;
        }

        return SemesterSchedule::with('semester')
            ->where('school_session_id', $session->id)
            ->whereDate('semester_start', '<=', now())
            ->whereDate('semester_end', '>=', now())
            ->first()?->semester;
    }
}

if (!function_exists('ict_roles')) {
    function ict_roles(): array
    {
        return [
            Role::ICT,
        ];
    }
}
if (!function_exists('main_staff_roles')) {
    function main_staff_roles(): array
    {
        return [
            Role::ICT,
            Role::ADMIN,
            Role::REGISTRAR,
        ];
    }
}

if (!function_exists('main_staff_plus_bursar_roles')) {
    function main_staff_plus_bursar_roles(): array
    {
        return array_merge(main_staff_roles(), [
            Role::BURSAR,
        ]);
    }
}

if (!function_exists('management_staff_roles')) {
    function management_staff_roles(): array
    {
        return array_merge(main_staff_roles(), [
            Role::PROPRIETOR,
            Role::PROVOST,
            Role::DEPUTY_PROVOST,
            Role::BURSAR,
            Role::LIBRARIAN,
            Role::EXAMINER,
        ]);
    }
}

if (!function_exists('all_staff_roles')) {
    function all_staff_roles(): array
    {
        return array_merge(management_staff_roles(), [
            Role::HOD,
        ]);
    }
}

if (!function_exists('main_staff_access')) {
    function main_staff_access(): bool
    {
        return Auth::check() && in_array(Auth::user()->role, main_staff_roles());
    }
}

if (!function_exists('ict_access')) {
    function ict_access(): bool
    {
        return Auth::check() && in_array(Auth::user()->role, ict_roles());
    }
}

if (!function_exists('main_staff_plus_bursar_access')) {
    function main_staff_plus_bursar_access(): bool
    {
        return Auth::check() && in_array(Auth::user()->role, main_staff_plus_bursar_roles());
    }
}

if (!function_exists('management_staff_access')) {
    function management_staff_access(): bool
    {
        return Auth::check() && in_array(Auth::user()->role, management_staff_roles());
    }
}

if (!function_exists('all_staff_access')) {
    function all_staff_access(): bool
    {
        return Auth::check() && in_array(Auth::user()->role, all_staff_roles());
    }
}
