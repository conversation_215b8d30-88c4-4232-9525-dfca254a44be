<?php

namespace App\Filament\Staff\Clusters\Settings\Pages;

use App\Enums\Role;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Filament\Staff\Clusters\Settings;
use App\Settings\PortalSettings;

class ManagePortal extends SettingsPage
{
    protected static string $settings = PortalSettings::class;
    protected static ?string $cluster = Settings::class;
    protected static ?string $navigationLabel = 'Portal';
    protected static ?int $navigationSort = 2;

    public static function canAccess(): bool
    {
        return management_staff_access();
    }
    protected function isDisabled(): bool
    {
        return !in_array(Auth::user()->role, [Role::ICT]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('fee')
                            ->label('Fee')
                            ->schema([
                                TextInput::make('portal_fee')
                                    ->disabled($this->isDisabled())
                                    ->prefix('₦')
                                    ->required()
                                    ->numeric()
                                    ->minValue(1000)
                                    ->maxValue(100000)
                                    ->extraAttributes(['style' => 'width: 120px;']),

                            ]),
                    ])
            ]);
    }

    public function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Settings Updated')
            ->body('The portal settings have been saved successfully.');
    }
}
