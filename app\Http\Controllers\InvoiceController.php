<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Models\User;
use App\Models\Invoice;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;

class InvoiceController extends Controller
{

    public function print(Invoice $invoice, $due = null)
    {
        return view('filament.documents.invoice', $this->getInvoiceData($invoice, $due));
    }

    public function download(Invoice $invoice, $due = null)
    {
        return Pdf::view('filament.documents.invoice', $this->getInvoiceData($invoice, $due))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name('Invoice -' . $invoice->user->name . '.pdf')
            ->download();
    }

    private function getInvoiceData(Invoice $invoice, $due = null)
    {
        $collegeSettings = app(CollegeSettings::class);

        $bursar = User::where('role', Role::BURSAR)->first();

        return [
            'invoice' => $invoice,
            'collegeSettings' => $collegeSettings,
            'bursar' => $bursar,
            'due' => $due
        ];
    }
}
