<?php

namespace App\Filament\Staff\Resources;


use Filament\Tables;
use Filament\Forms\Form;
use App\Models\Assessment;
use Filament\Tables\Table;
use Illuminate\Support\Number;
use App\Enums\AssessmentStatus;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\Summarizers\Sum;
use App\Filament\Staff\Resources\AssessmentResource\Pages;

class AssessmentResource extends Resource
{
    protected static ?string $model = Assessment::class;
    protected static ?int $navigationSort = 8;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of assessments';

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->label('Assessment')
                    ->placeholder('1st C.A.')
                    ->maxLength(10)
                    ->datalist([
                        'C.A',
                        '1st C.A.',
                        '2nd C.A.',
                        '3rd C.A.',
                        'Exam'
                    ])
                    ->unique(ignoreRecord: true),
                TextInput::make('max_score')
                    ->label('Max. score')
                    ->suffix('%')
                    ->required()
                    ->numeric()
                    ->maxValue(100)
                    ->minValue(0)
                    ->placeholder('20'),
                Select::make('order')
                    ->required()
                    ->label('Order')
                    ->options([
                        1 => '1st',
                        2 => '2nd',
                        3 => '3rd',
                        4 => '4th',
                        5 => '5th',
                    ])
                    ->placeholder('Select an order')
                    ->unique(ignoreRecord: true),
            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('order')
            ->emptyStateHeading('No Assessments Yet')
            ->emptyStateDescription('Once you create your first assessment, it will appear here.')
            ->description(new HtmlString("List of assessments to evaluate students' performance effectively.<br>
            <b>NOTE:</b> Make sure the total maximum score is 100 to avoid errors."))

            ->columns([
                TextColumn::make('#')
                    ->rowIndex()
                    ->width('2rem'),
                TextColumn::make('name')
                    ->width('2rem'),
                TextColumn::make('max_score')
                    ->width('2rem')
                    ->label('Max. mark')
                    ->summarize(
                        Sum::make()
                            ->label('Total')
                    ),
                TextColumn::make('order')
                    ->formatStateUsing(fn($state) => Number::ordinal($state))
            ])

            ->filters([])

            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Assessment Updated')
                                ->body('The assessment has been updated successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Assessment?')
                        ->modalDescription('Are you sure you want to delete this assessment?')
                        ->modalSubmitActionLabel('Yes, delete it')
                        ->modalCancelActionLabel('No, keep it')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['scores'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this assessment because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Assessment Deleted')
                                ->body('The assessment has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Assessment actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAssessments::route('/'),
        ];
    }
}
