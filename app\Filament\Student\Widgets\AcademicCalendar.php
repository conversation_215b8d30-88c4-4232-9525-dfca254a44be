<?php

namespace App\Filament\Student\Widgets;

use App\Enums\Role;
use App\Enums\AdmissionStatus;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\IconPosition;
use App\Services\AcademicCalendarService;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class AcademicCalendar extends BaseWidget
{

    protected static ?string $pollingInterval = null;
    protected ?string $heading = 'Academic Calendar';
    protected ?string $description = 'Stay updated on key academic dates and resumptions.';
    protected static ?int $sort = 1;

    public static function canView(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }

    protected function getStats(): array
    {
        $calendar = app(AcademicCalendarService::class);
        return [
            Stat::make('Active Session', $calendar->getCurrentSession())
                ->description(new HtmlString($calendar->getCurrentSessionPeriod()))
                ->descriptionIcon('heroicon-m-calendar-days', IconPosition::Before)
                ->color('info'),

            Stat::make('Current Semester', $calendar->getCurrentSemester())
                ->description(new HtmlString($calendar->getCurrentSemesterPeriod()))
                ->descriptionIcon('heroicon-m-calendar-days', IconPosition::Before)
                ->color('info'),

            Stat::make('Next Semester', $calendar->getNextSemester())
                ->description(new HtmlString($calendar->getNextSemesterPeriod()))
                ->descriptionIcon('heroicon-m-calendar-days', IconPosition::Before)
                ->color('info'),
        ];
    }
}
