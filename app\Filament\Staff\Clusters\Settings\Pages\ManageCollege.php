<?php

namespace App\Filament\Staff\Clusters\Settings\Pages;

use App\Enums\Role;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use App\Settings\CollegeSettings;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Filament\Staff\Clusters\Settings;
use Filament\Forms\Components\FileUpload;

class ManageCollege extends SettingsPage
{

    protected static string $settings = CollegeSettings::class;
    protected static ?string $cluster = Settings::class;
    protected static ?string $navigationLabel = 'College';
    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        return management_staff_access();
    }

    protected function isDisabled(): bool
    {
        return !in_array(Auth::user()->role, [Role::ICT, Role::REGISTRAR]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('information')
                            ->label('Information')
                            ->schema([
                                TextInput::make('name')
                                    ->disabled($this->isDisabled())
                                    ->required()
                                    ->minLength(5)
                                    ->maxLength(50),
                                TextInput::make('motto')
                                    ->disabled($this->isDisabled())
                                    ->required()
                                    ->minLength(5)
                                    ->maxLength(50),
                                TextInput::make('address')
                                    ->disabled($this->isDisabled())
                                    ->label('Full address')
                                    ->placeholder('Okinni Osogbo, Osun State, Nigeria')
                                    ->required()
                                    ->minLength(10)
                                    ->maxLength(65),
                                TextInput::make('email')
                                    ->disabled($this->isDisabled())
                                    ->email()
                                    ->required(),
                                TextInput::make('phone')
                                    ->disabled($this->isDisabled())
                                    ->tel()
                                    ->numeric()
                                    ->maxLength(11)
                                    ->required(),

                                FileUpload::make('stamp')
                                    ->required()
                                    ->disabled($this->isDisabled())
                                    ->directory('documents')
                                    ->downloadable()
                                    ->image()
                                    ->minSize(5)
                                    ->maxSize(200)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Stamp size must be between 5kb-200kb.')
                                    ->uploadingMessage('Uploading stamp...'),
                            ])->columns(2),
                        Tabs\Tab::make('user')
                            ->label('User')
                            ->schema([
                                TextInput::make('ict_email')
                                    ->disabled($this->isDisabled())
                                    ->label('ICT/Helpdesk Email')
                                    ->email()
                                    ->required(),
                                TextInput::make('ict_phone')
                                    ->disabled($this->isDisabled())
                                    ->label('ICT/Helpdesk Phone')
                                    ->tel()
                                    ->numeric()
                                    ->maxLength(11)
                                    ->required(),
                                FileUpload::make('registrar_sign')
                                    ->required()
                                    ->disabled($this->isDisabled())
                                    ->directory('documents')
                                    ->downloadable()
                                    ->image()
                                    ->minSize(5)
                                    ->maxSize(200)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Signature size must be between 5kb-200kb.')
                                    ->uploadingMessage('Uploading signature...'),
                            ])->columns(2),
                    ])
            ]);
    }

    public function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Settings Updated')
            ->body('The college settings have been saved successfully.');
    }
}
