<?php

namespace App\Models;

use App\Enums\FeeType;
use App\Casts\MoneyCast;
use App\Enums\InvoiceStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Invoice extends Model
{
    use SoftDeletes;

    protected $casts = [
        'total_amount' => MoneyCast::class,
        'invoice_status' => InvoiceStatus::class,
        'fee_type' => FeeType::class,
        'expired_at' => 'datetime',
        'paid_at' => 'datetime',
        'description' => 'array',
        'metadata' => 'array',
        'payment_success' => 'boolean',
    ];

    public function getPeriodAttribute()
    {
        return [
            'level' => 'Level: ' . $this->level?->name,
            'semester' => 'Semester: ' . $this->semester?->name,
            'session' => 'Session: ' . $this->schoolSession?->name,
        ];
    }

    /**
     * Get the user that owns the Invoice
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the fee that owns the Invoice
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function fee(): BelongsTo
    {
        return $this->belongsTo(Fee::class);
    }

    /**
     * Get the transaction that owns the Invoice
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function transaction(): HasOne
    {
        return $this->hasOne(Transaction::class);
    }


    public function getSchoolSessionAttribute()
    {
        return $this->payable?->schoolSession;
    }

    public function getSemesterAttribute()
    {
        return $this->payable?->semester;
    }

    public function getLevelAttribute()
    {
        return $this->payable?->level;
    }

    /**
     * Get the payable that owns the Invoice
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }
}
