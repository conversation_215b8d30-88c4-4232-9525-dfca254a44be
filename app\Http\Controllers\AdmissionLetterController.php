<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Models\User;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;

class AdmissionLetterController extends Controller
{

    public function print(User $student)
    {
        return view('filament.documents.admission-letter', $this->getAdmissionLetterData($student));
    }

    public function download(User $student)
    {
        return Pdf::view('filament.documents.admission-letter', $this->getAdmissionLetterData($student))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name('Admission letter - ' . $student->name . '.pdf')
            ->download();
    }

    private function getAdmissionLetterData(User $student)
    {
        $collegeSettings = app(CollegeSettings::class);

        $registrar = User::where('role', Role::REGISTRAR)->first();

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
            'registrar' => $registrar
        ];
    }
}
