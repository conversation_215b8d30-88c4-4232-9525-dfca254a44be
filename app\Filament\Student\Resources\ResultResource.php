<?php

namespace App\Filament\Student\Resources;

use App\Enums\Role;
use App\Models\User;
use App\Models\Grade;
use App\Models\Level;
use App\Models\Score;
use App\Models\Course;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Semester;
use App\Models\Department;
use App\Models\Scoresheet;
use App\Models\TotalScore;
use Filament\Tables\Table;
use App\Enums\InvoiceStatus;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use Filament\Resources\Resource;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Student\Resources\ResultResource\Pages;

class ResultResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?int $navigationSort = 4;
    protected static ?string $modelLabel = 'result';
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status === AdmissionStatus::APPROVED;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('id', Auth::id())
            ->where('role', Role::STUDENT);
    }

    public static function table(Table $table): Table
    {
        $student = Auth::user();
        $student->loadMissing(['registrations.schoolSession', 'registrations.level']);

        return $table
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->paginated(false)
            ->recordUrl(null)
            ->recordAction(null)
            ->emptyStateHeading(fn(HasTable $livewire) => self::getEmptyStateHeading($livewire, $student))
            ->emptyStateDescription(fn(HasTable $livewire) => new HtmlString(self::getEmptyStateDescription($livewire, $student)))
            ->description('This section displays your academic result when available.')
            ->columns([
                ViewColumn::make('')
                    ->view('filament.tables.result'),
            ])
            ->filters([
                Filter::make('result_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->live()
                            ->required()
                            ->label('Session')
                            ->options(
                                $student->registrations
                                    ->sortByDesc('schoolSession.name')
                                    ->pluck('schoolSession.name', 'school_session_id')
                                    ->unique()
                            )
                            ->native(false)
                            ->default($student->activeSchoolSession?->id)
                            ->exists('school_sessions', 'id')
                            ->afterStateUpdated(fn(Set $set) => $set('level_id', null)),
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->relationship('semesters', 'name')
                            ->native(false)
                            ->default($student->activeSemester?->id)
                            ->exists('semesters', 'id'),
                        Select::make('level_id')
                            ->required()
                            ->label('Level')
                            ->placeholder(fn(Get $get) => empty($get('school_session_id')) ? 'Select a session first' : 'Select a level')
                            ->options(function (Get $get) use ($student) {
                                if (empty($get('school_session_id'))) {
                                    return [];
                                }
                                $sessionId = $get('school_session_id');

                                return $student->registrations
                                    ->where('school_session_id', $sessionId)
                                    ->pluck('level.name', 'level_id')
                                    ->unique();
                            })
                            // ->native(false)
                            ->default($student->activeLevel?->id)
                            ->exists('levels', 'id'),
                        Select::make('department_id')
                            ->required()
                            ->label('Department')
                            ->native(false)
                            ->options(function () use ($student) {

                                $programmeDeptIds = $student->activeRegistration?->programme?->departments->pluck('id') ?? [];
                                return Department::whereIn('id', $programmeDeptIds)
                                    ->orWhere('is_edu', true)
                                    ->orWhere('is_gse', true)
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->exists('departments', 'id'),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, HasTable $livewire) use ($student): Builder {
                        if (!self::hasRequiredFilters($livewire) || !self::isScoresheetAvailable($livewire) || !self::isPortalFeePaid($livewire, $student) || !self::isBiodataComplete()) {
                            return $query->whereRaw('1 = 0');
                        }
                        return $query;
                    })
                    ->query(
                        fn(Builder $query, array $data) => $query->whereHas('registrations', function ($q) use ($data) {
                            $q->where('school_session_id', $data['school_session_id'])
                                ->where('semester_id', $data['semester_id'])
                                ->where('level_id', $data['level_id']);
                        })
                    )
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            $name = SchoolSession::find($sessionId)?->name;
                            if ($name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            $name = Semester::find($semesterId)?->name;
                            if ($name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        if ($levelId = $data['level_id'] ?? null) {
                            $name = Level::find($levelId)?->name;
                            if ($name) {
                                $indicators[] = Indicator::make("Level: {$name}")->removable(false);
                            }
                        }

                        if ($deptId = $data['department_id'] ?? null) {
                            $name = Department::find($deptId)?->name;
                            if ($name) {
                                $indicators[] = Indicator::make("Department: {$name}")->removable(false);
                            }
                        }

                        return $indicators;
                    })

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn(Action $action) => $action->label('View result'),
            );
    }

    public static function getSemesterCourseData(HasTable $livewire, User $student): array
    {
        $filters = self::extractFilters($livewire);
        if (!self::hasRequiredFilters($livewire) || !self::isScoresheetPublished($livewire)) {
            return [];
        }

        $registration = self::getRegistration($livewire, $student->id);
        if (!$registration) {
            return [];
        }

        // Fetch courses for the selected filters
        $courses = Course::select('id', 'code', 'title', 'credit', 'course_status')
            ->where('semester_id', $filters['semester_id'])
            ->where('level_id', $filters['level_id'])
            ->where('department_id', $filters['department_id'])
            ->orderBy('code')
            ->get();

        $grades = Grade::all();

        return $courses->map(function ($course) use ($registration, $grades) {

            $scores = Score::with('assessment')
                ->where('course_id', $course->id)
                ->where('registration_id', $registration->id)
                ->get();

            $totalScore = TotalScore::where('course_id', $course->id)
                ->where('registration_id', $registration->id)
                ->value('total');

            $grade = self::getGradeFromScore($totalScore, $grades);
            $assessmentScores = $scores->mapWithKeys(fn($score) => [
                $score->assessment->name => $score->score,
            ])->all();

            return [
                'code' => $course->code,
                'status' => $course->course_status,
                'title' => $course->title,
                'credit' => $course->credit,
                ...$assessmentScores,
                'total_score' => $totalScore,
                'grade' => $grade?->name,
                'point' => $grade?->point,
                'grade_point' => ($grade ? $grade->point * $course->credit : null),
            ];
        })->toArray();
    }

    private static function getSemesterGradePoint(Registration $registration, Course $course, Collection $grades): ?float
    {
        $totalScore = TotalScore::where('registration_id', $registration->id)
            ->where('course_id', $course->id)
            ->value('total');

        $grade = self::getGradeFromScore($totalScore, $grades);
        return $grade ? (float) ($grade->point * $course->credit) : null;
    }

    public static function getSemesterTotalGradePoint(array $courseData): ?float
    {
        if (empty($courseData)) {
            return null;
        }
        return collect($courseData)->sum('grade_point') ?? null;
    }

    public static function getSemesterTotalCreditUnit(array $courseData): int
    {
        if (empty($courseData)) {
            return 0;
        }
        return collect($courseData)->sum('credit');
    }

    public static function getSemesterOutstandingCourses(array $courseData): Collection
    {

        return collect($courseData)->filter(fn($course) => ($course['total_score'] ?? -1) <= self::getFailedScore());
    }

    public static function getCumulativeTotalGradePoint(HasTable $livewire, User $student): float
    {
        $filters = self::extractFilters($livewire);
        $totalGradePoint = 0.0;
        $grades = Grade::all();

        foreach ($student->registrations as $registration) {
            if (!self::isScoresheetPublishedForRegistration($registration, $filters['department_id'])) {
                continue;
            }

            $courses = Course::select('id', 'credit')
                ->where('level_id', $registration->level_id)
                ->where('semester_id', $registration->semester_id)
                ->where('department_id', $filters['department_id'])
                ->get();

            foreach ($courses as $course) {
                $gradePoint = self::getSemesterGradePoint($registration, $course, $grades);
                if ($gradePoint !== null) {
                    $totalGradePoint += $gradePoint;
                }
            }
        }

        return $totalGradePoint;
    }

    public static function getCumulativeTotalCreditUnit(HasTable $livewire, User $student): int
    {
        $filters = self::extractFilters($livewire);
        $totalCredit = 0;

        foreach ($student->registrations as $registration) {
            if (!self::isScoresheetPublishedForRegistration($registration, $filters['department_id'])) {
                continue;
            }

            $courses = Course::select('credit')
                ->where('level_id', $registration->level_id)
                ->where('semester_id', $registration->semester_id)
                ->where('department_id', $filters['department_id'])
                ->get();

            $totalCredit += $courses->sum('credit');
        }

        return $totalCredit;
    }

    public static function getCumulativeOutstandingCourses(HasTable $livewire, User $student): Collection
    {
        $filters = self::extractFilters($livewire);
        $failedCourses = collect();
        $failedScore = self::getFailedScore();

        // Collect all published combinations of level, semester, and session
        $publishedCombos = $student->registrations->filter(function ($reg) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        });

        foreach ($publishedCombos as $registration) {
            $courses = Course::select('id', 'code')
                ->where('level_id', $registration->level_id)
                ->where('semester_id', $registration->semester_id)
                ->where('department_id', $filters['department_id'])
                ->get();

            foreach ($courses as $course) {
                $totalScore = TotalScore::where([
                    'registration_id' => $registration->id,
                    'course_id' => $course->id,
                ])->value('total');

                if (($totalScore ?? -1) <= $failedScore) {
                    $failedCourses->push($course);
                }
            }
        }

        return $failedCourses->unique('id');
    }

    public static function getGradeFromScore(?int $totalScore, ?Collection $grades = null)
    {
        if ($totalScore === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();
        return $grades->first(
            fn($grade) => $totalScore >= $grade->min_score && $totalScore <= $grade->max_score
        );
    }

    public static function getRemarkFromGradePointAverage(?float $gradePointAverage, ?Collection $grades = null)
    {
        if ($gradePointAverage === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();

        return $grades->first(
            fn($grade) => $gradePointAverage >= $grade->min_point && $gradePointAverage <= $grade->max_point
        );
    }

    public static function getFailedScore(): int
    {
        static $failedScore;
        $failedScore ??= Grade::where('min_score', 0)->value('max_score');
        return $failedScore;
    }

    private static function getRegistration(HasTable $livewire, int $userId): ?Registration
    {
        $filters = self::extractFilters($livewire);

        if (!self::hasRequiredFilters($livewire)) {
            return null;
        }

        return Registration::where('user_id', $userId)
            ->where('school_session_id', $filters['school_session_id'])
            ->where('semester_id', $filters['semester_id'])
            ->where('level_id', $filters['level_id'])
            ->first();
    }

    public static function isPortalFeePaid(HasTable $livewire, User $student): bool
    {
        $filters = self::extractFilters($livewire);
        if (!self::hasRequiredFilters($livewire)) {
            return false;
        }

        return $student->registrations()->where('school_session_id', $filters['school_session_id'])
            ->where('semester_id', $filters['semester_id'])
            ->whereHas('portalInvoice', function ($query) {
                $query->where('invoice_status', InvoiceStatus::PAID);
            })
            ->exists();
    }

    public static function isScoresheetCreated(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);
        if (!self::hasRequiredFilters($livewire)) {
            return false;
        }
        return Scoresheet::where('school_session_id', $filters['school_session_id'])
            ->where('semester_id', $filters['semester_id'])
            ->where('department_id', $filters['department_id'])
            ->exists();
    }

    public static function isScoresheetPublished(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);
        if (!self::hasRequiredFilters($livewire)) {
            return false;
        }
        return Scoresheet::where('school_session_id', $filters['school_session_id'])
            ->where('semester_id', $filters['semester_id'])
            ->where('department_id', $filters['department_id'])
            ->where('is_published', true)
            ->exists();
    }

    public static function isBiodataComplete(): bool
    {
        return Auth::user()->isBiodataComplete();
    }

    private static function isScoresheetAvailable(HasTable $livewire): bool
    {
        return self::isScoresheetCreated($livewire) && self::isScoresheetPublished($livewire);
    }

    private static function isScoresheetPublishedForRegistration(Registration $registration, ?int $departmentId): bool
    {
        if (!$departmentId) {
            return false;
        }
        return Scoresheet::where('school_session_id', $registration->school_session_id)
            ->where('semester_id', $registration->semester_id)
            ->where('department_id', $departmentId)
            ->where('is_published', true)
            ->exists();
    }

    private static function getEmptyStateHeading(HasTable $livewire, User $student): string
    {
        if (!self::hasRequiredFilters($livewire)) {
            return 'All Options Must Be Selected to View Results';
        } elseif (!self::isPortalFeePaid($livewire, $student)) {
            return 'Portal Fee is Not Paid';
        } elseif (!self::isBiodataComplete()) {
            return 'Incomplete Bio-data';
        } elseif (!self::isScoresheetCreated($livewire)) {
            return 'No Result Yet';
        } elseif (!self::isScoresheetPublished($livewire)) {
            return 'Result is Under Processing';
        }

        return 'No Result Found';
    }

    private static function getEmptyStateDescription(HasTable $livewire, User $student): string
    {
        if (!self::hasRequiredFilters($livewire)) {
            return 'Select <b>session</b>, <b>semester</b>, <b>level</b>, and <b>department</b> to view your result.';
        } elseif (!self::isPortalFeePaid($livewire, $student)) {
            return 'Please pay the portal fee for the selected semester to view your result.';
        } elseif (!self::isBiodataComplete()) {
            return 'Please update your bio-data to view your result.';
        } elseif (!self::isScoresheetCreated($livewire)) {
            return 'Once your result is uploaded, it will appear here.';
        } elseif (!self::isScoresheetPublished($livewire)) {
            return 'Your result is being processed. Please check back later.';
        }

        return 'No result found for the selected options.';
    }

    private static function extractFilters(HasTable $livewire): array
    {
        return $livewire->tableFilters['result_filter'] ?? [
            'school_session_id' => null,
            'semester_id' => null,
            'level_id' => null,
            'department_id' => null,
        ];
    }

    private static function hasRequiredFilters(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return !empty($filters['school_session_id'])
            && !empty($filters['semester_id'])
            && !empty($filters['level_id'])
            && !empty($filters['department_id']);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageResults::route('/'),
        ];
    }
}
