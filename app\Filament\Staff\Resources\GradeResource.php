<?php

namespace App\Filament\Staff\Resources;


use Closure;
use Filament\Tables;
use App\Models\Grade;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Section;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use App\Filament\Staff\Resources\GradeResource\Pages;

class GradeResource extends Resource
{
    protected static ?string $model = Grade::class;
    protected static ?int $navigationSort = 9;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of grades';

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Grade per course')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->label('Grade')
                            ->placeholder('F')
                            ->maxLength(5)
                            ->datalist([
                                'A',
                                'B',
                                'C',
                                'D',
                                'E',
                                'F',
                            ])
                            ->unique(ignoreRecord: true),
                        TextInput::make('min_score')
                            ->required()
                            ->numeric()
                            ->maxValue(100)
                            ->minValue(0)
                            ->label('Min. score')
                            ->placeholder('0'),
                        TextInput::make('max_score')
                            ->required()
                            ->numeric()
                            ->maxValue(100)
                            ->minValue(0)
                            ->label('Max. mark')
                            ->placeholder('39')
                            ->rule(function (callable $get) {
                                return function (string $attribute, $value, Closure $fail) use ($get) {
                                    $minScore = $get('min_score');
                                    if ($value <= $minScore) {
                                        $fail('Maximum score must be greater than minimum score.');
                                    }
                                };
                            }),
                        TextInput::make('remark')
                            ->required()
                            ->maxLength(10)
                            ->placeholder('Fail'),
                    ])->columns(4),
                Section::make('Pass level')
                    ->schema([
                        TextInput::make('min_point')
                            ->required()
                            ->numeric()
                            ->maxValue(5)
                            ->minValue(0)
                            ->label('Minimum grade point')
                            ->placeholder('0.00'),
                        TextInput::make('max_point')
                            ->required()
                            ->numeric()
                            ->maxValue(5)
                            ->minValue(0)
                            ->label('Maximum grade point')
                            ->placeholder('0.99')
                            ->rule(function (callable $get) {
                                return function (string $attribute, $value, Closure $fail) use ($get) {
                                    $minGradePoint = $get('min_point');
                                    if ($value <= $minGradePoint) {
                                        $fail('Maximum grade point must be greater than minimum grade point.');
                                    }
                                };
                            }),
                    ])->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('name')
            ->emptyStateHeading('No Grades Yet')
            ->emptyStateDescription('Once you create your first grade, it will appear here.')
            ->description('The list of grades created for efficient assessments evaluation.')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->badge(),
                TextColumn::make('min_score')
                    ->label('Min. mark'),
                TextColumn::make('max_score')
                    ->label('Max. mark'),
                TextColumn::make('point')
                    ->label('Point')
                    ->badge(),
                TextColumn::make('min_point')
                    ->label('Min. point'),
                TextColumn::make('max_point')
                    ->label('Max. point'),
                TextColumn::make('remark')
                    ->badge(),

            ])

            ->filters([])

            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Grade Updated')
                                ->body('The grade has been updated successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Grade?')
                        ->modalDescription('Are you sure you want to delete this grade?')
                        ->modalSubmitActionLabel('Yes, delete it')
                        ->modalCancelActionLabel('No, keep it')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['scores'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this grade because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Grade Deleted')
                                ->body('The grade has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Grade actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageGrades::route('/'),
        ];
    }
}
