<?php

namespace App\Models;

use App\Casts\MoneyCast;
use App\Enums\InvoiceStatus;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Fee extends Model
{
    use HasFactory;

    protected $casts = [
        'total_amount' => MoneyCast::class,
        'description' => 'array',
        'is_scheduled' => 'boolean',
    ];

    /**
     * Get the feeCategory that owns the Fee
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function feeCategory(): BelongsTo
    {
        return $this->belongsTo(FeeCategory::class);
    }

    /**
     * Get the schoolSession that owns the Fee
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function schoolSession(): BelongsTo
    {
        return $this->belongsTo(SchoolSession::class);
    }

    /**
     * Get the semester that owns the Fee
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    /**
     * Get the level that owns the Fee
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(Level::class);
    }


    /**
     * Get the period for the Fee
     *
     * @return array
     */
    public function getPeriodAttribute()
    {
        return [
            'level' => 'Level: ' . $this->level->name,
            'semester' => 'Semester: ' . $this->semester->name,
            'session' => 'Session: ' . $this->schoolSession->name,
        ];
    }

    /**
     * Get the paid invoices for the Fee
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function paidInvoices()
    {
        return $this->invoices()
            ->where('invoice_status', InvoiceStatus::PAID)
            ->where('user_id', Auth::id());
    }

    /**
     * Get the paid amount for the Fee
     *
     * @return int
     */
    public function getPaidAmountAttribute()
    {
        return $this->paidInvoices()->sum('amount');
    }

    /**
     * Get the due amount for the Fee
     *
     * @return int
     */
    public function getDueAmountAttribute()
    {
        return (float) $this->total_amount - $this->paid_amount;
    }

    /**
     * Get the invoices for the Fee
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function invoices(): MorphMany
    {
        return $this->morphMany(Invoice::class, 'payable');
    }
}
