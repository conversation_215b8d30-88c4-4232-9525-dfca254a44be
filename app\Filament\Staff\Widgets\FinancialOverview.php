<?php

// namespace App\Filament\Staff\Widgets;

// use App\Enums\Role;
// use App\Models\Fee;
// use App\Models\User;
// use App\Models\Expense;
// use App\Models\Transaction;
// use Filament\Facades\Filament;
// use App\Enums\TransactionStatus;
// use Illuminate\Support\HtmlString;
// use Illuminate\Support\Facades\Log;
// use Illuminate\Support\Facades\Auth;
// use Filament\Support\Enums\IconPosition;
// use Filament\Widgets\StatsOverviewWidget\Stat;
// use Filament\Widgets\StatsOverviewWidget as BaseWidget;

// class FinancialOverview extends BaseWidget
// {
//     protected static ?string $pollingInterval = '10s';
//     protected ?string $heading = 'Financial Overview';
//     protected ?string $description = 'Track all school income, expenses, and financial activities for the current session.';
//     protected static ?int $sort = 2;


//     public static function canView(): bool
//     {
//         $user = Auth::user();

//         return $user && in_array($user->role, all_staff_roles());
//     }


//     protected function getStats(): array
//     {
//         return [
//             Stat::make('Fees Due', $this->getTotalDue())
//                 ->description(new HtmlString('Outstanding fees from students.'))
//                 ->descriptionIcon('heroicon-m-document-minus', IconPosition::Before)
//                 ->color('info'),

//             Stat::make('Payments Received', $this->getTotalIncome())
//                 ->description(new HtmlString('All payments collected.'))
//                 ->descriptionIcon('heroicon-m-credit-card', IconPosition::Before)
//                 ->color('success'),

//             Stat::make('Expenses Paid', $this->getTotalExpenses())
//                 ->description('Money spent on operations.')
//                 ->descriptionIcon('heroicon-m-banknotes', IconPosition::Before)
//                 ->color('danger'),
//         ];
//     }

//     private function getTotalDueRaw(): int
//     {
//         $schoolId = Filament::getTenant()->id;
//         $sessionId = currentSchoolSession()?->id;

//         $fees = Fee::where('school_id', $schoolId)
//             ->where('school_session_id', $sessionId)
//             ->where('is_scheduled', true)
//             ->get();

//         $studentsByCategory = User::where('role', Role::STUDENT)
//             ->where('school_id', $schoolId)
//             ->with(['category', 'enrollments'])
//             ->get()
//             ->groupBy(fn($student) => $student->category_id);

//         $transactions = Transaction::where('transaction_status', TransactionStatus::PAID)
//             ->where('school_id', $schoolId)
//             ->where('school_session_id', $sessionId)
//             ->whereIn('fee_id', $fees->pluck('id'))
//             ->get();

//         $transactionsGrouped = $transactions->groupBy(['fee_id', 'user_id']);

//         $totalDue = 0;

//         foreach ($fees as $fee) {

//             if ($fee->category_id !== null) {
//                 $students = $studentsByCategory->get($fee->category_id, collect());
//             } else {
//                 $students = User::where('role', Role::STUDENT)->where('school_id', $schoolId)->get();
//             }

//             $studentsToCharge = $students->filter(function ($student) use ($fee, $sessionId) {
//                 $enrollment = $student->enrollments->firstWhere('school_session_id', $sessionId);


//                 if (!$enrollment) {
//                     return false;
//                 }

//                 if ($fee->section_id !== null) {
//                     return $enrollment->section_id === $fee->section_id;
//                 }

//                 if ($fee->school_class_id !== null) {
//                     return $enrollment->school_class_id === $fee->school_class_id;
//                 }

//                 return true;
//             });

//             foreach ($studentsToCharge as $student) {
//                 $paidAmount = $transactionsGrouped
//                     ->get($fee->id, collect())
//                     ->get($student->id, collect())
//                     ->sum('amount');

//                 $dueAmount = $fee->total_amount - $paidAmount;

//                 if ($dueAmount > 0) {
//                     $totalDue += $dueAmount;
//                 }
//             }
//         }

//         return $totalDue;
//     }

//     public function getTotalDue(): string
//     {
//         return '₦' . number_format($this->getTotalDueRaw());
//     }


//     private function getTotalIncome()
//     {
//         $transactions = Transaction::where('transaction_status', TransactionStatus::PAID)
//             ->where('school_id', Filament::getTenant()->id)
//             ->where('school_session_id', currentSchoolSession()?->id)->get();

//         return '₦' . number_format($transactions->sum('amount'));
//     }

//     private function getTotalExpenses()
//     {
//         $expenses = Expense::where('approved_at', '!=', null)
//             ->where('school_id', Filament::getTenant()->id)
//             ->where('school_session_id', currentSchoolSession()?->id)->get();

//         return '₦' . number_format($expenses->sum('total_amount'));
//     }
// }
