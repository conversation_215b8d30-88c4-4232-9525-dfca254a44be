@php
use App\Filament\Student\Pages\Auth\StudentProfile;
use App\Filament\Staff\Pages\Auth\StaffProfile;
use App\Enums\Role;

$user = Auth::user();
$isStudent = $user->role === Role::STUDENT;
$profileUrl = $isStudent
    ? StudentProfile::getUrl(['panel' => 'student'])
    : StaffProfile::getUrl(['panel' => 'staff']);
@endphp

@if(!$user->isBiodataComplete())
    <div class="bg-amber-100 border border-amber-400 text-amber-800 px-4 py-2 rounded-sm flex items-center justify-center gap-2 mt-4 text-sm font-semibold">
        <x-filament::icon icon="heroicon-s-exclamation-circle" class="w-5 h-5 text-amber-600"/>
        Your bio-data is incomplete. Please update your details.
        <a href="{{ $profileUrl }}" class="text-blue-600 underline">Bio-data page</a>
    </div>
@endif
