<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Programme extends Model
{

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the first department that owns the SubjectCombination
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function firstDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'first_department_id', 'id');
    }

    /**
     * Get the second department that owns the SubjectCombination
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function secondDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'second_department_id', 'id');
    }

    public function getDepartmentsAttribute()
    {
        return Department::whereIn('id', [$this->first_department_id, $this->second_department_id])->get();
    }
}
