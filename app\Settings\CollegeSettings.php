<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class CollegeSettings extends Settings
{
    public ?string $name;
    public ?string $motto;
    public ?string $address;
    public ?string $phone;
    public ?string $email;
    public ?string $ict_phone;
    public ?string $ict_email;
    public ?string $stamp = null;
    public ?string $registrar_sign = null;

    public static function default(): array
    {
        return [
            'name' => 'Raphat College of Education Obaagun',
            'motto' => 'Where Students are Achievers',
            'address' => 'Km 2, Iree Expressway, Igbota Area, Obaagun, Osun State.',
            'phone' => '08034567890',
            'email' => '<EMAIL>',
            'ict_phone' => '08034567890',
            'ict_email' => '<EMAIL>',
            'stamp' => 'stamp.png',
            'registrar_sign' => 'registrar-sign.png',
        ];
    }

    public static function group(): string
    {
        return 'college';
    }

    public function getFormattedPhone(): string
    {
        return '+234' . ltrim($this->phone, '0');
    }
    public function getFormattedIctPhone(): string
    {
        return '+234' . ltrim($this->ict_phone, '0');
    }
}
