<?php
namespace App\Livewire;
use Livewire\Component;
use App\Models\Registration;
use Livewire\Attributes\On;
use App\Enums\Role;
use Illuminate\Support\Facades\Auth;

class RegistrationToggle extends Component
{
    public $registration;
    public $isActive;
    public $userId;
    public $isDisabled;

    public function mount(Registration $registration)
    {
        $this->registration = $registration;
        $this->isActive = $registration->is_active;
        $this->userId = $registration->user_id;
        $this->isDisabled = Auth::user()->role === Role::STUDENT;
    }

    public function toggle()
    {
        if ($this->isDisabled) {
            return;
        }

        $newStatus = !$this->isActive;
        
        if ($newStatus) {
            $this->registration->user->registrations()->update(['is_active' => false]);
            $this->dispatch('registration-toggled', userId: $this->userId, activeRegistrationId: $this->registration->id);
        }

        $this->registration->update(['is_active' => $newStatus]);
        $this->isActive = $newStatus;
    }

    #[On('registration-toggled')]
    public function onRegistrationToggled($userId, $activeRegistrationId)
    {
        if ($this->userId == $userId && $this->registration->id != $activeRegistrationId) {
            $this->isActive = false;
        }
    }

    public function render()
    {
        return view('livewire.registration-toggle');
    }
}