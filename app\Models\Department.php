<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Department extends Model
{
    //cast
    protected $casts = [
        'is_edu' => 'boolean',
        'is_gse' => 'boolean',
    ];
    public function name(): Attribute
    {
        return Attribute::get(fn($value) => Str::title($value));
    }
    /**
     * Get the school that owns the Department
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the head of department that owns the Department
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function headOfDepartment(): HasOne
    {
        return $this->hasOne(User::class);
    }
}
