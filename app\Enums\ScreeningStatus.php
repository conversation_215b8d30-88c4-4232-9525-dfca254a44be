<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum ScreeningStatus: int implements HasLabel, HasColor
{
    case PENDING = 0;
    case PASSED = 1;
    case FAILED = 2;

    public function getLabel(): ?string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::PASSED => 'Passed',
            self::FAILED => 'Failed',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::PENDING => 'gray',
            self::PASSED => 'success',
            self::FAILED => 'danger',
        };
    }
}
