<?php

namespace App\Filament\Staff\Pages\Auth;

use Filament\Forms\Form;
use Filament\Pages\Auth\EditProfile;
use App\Filament\Components\StaffForm;
use App\Filament\Staff\Pages\Dashboard;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;

class StaffProfile extends EditProfile
{

    public function getTitle(): string | Htmlable
    {
        return 'Bio-data';
    }

    public static function getSlug(): string
    {
        return 'bio-data';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(StaffForm::schema());
    }


    protected function getSavedNotification(): ?Notification
    {

        return Notification::make()
            ->success()
            ->title('Bio-data Updated')
            ->body('Your bio-data has been saved successfully');
    }

    protected function getRedirectUrl(): ?string
    {
        return Dashboard::getUrl(['panel' => 'staff']);
    }
}
