<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum TransactionStatus: int implements HasLabel, HasColor
{
    case PENDING = 0;
    case SUCCESS = 1;
    case PARTIAL = 2;
    case REVERSED = 3;
    case REFUNDED = 4;
    case CANCELLED = 5;
    case FAILED = 6;

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::SUCCESS => 'Success',
            self::PARTIAL => 'Partial',
            self::REVERSED => 'Reversed',
            self::REFUNDED => 'Refunded',
            self::CANCELLED => 'Cancelled',
            self::FAILED => 'Failed',
        };
    }

    /**
     * Get the color for the type
     */
    public function getColor(): string
    {
        return match ($this) {
            self::PENDING => 'gray',
            self::SUCCESS => 'success',
            self::PARTIAL => 'info',
            self::REVERSED => 'warning',
            self::REFUNDED => 'warning',
            self::CANCELLED => 'danger',
            self::FAILED => 'danger',
        };
    }

    public function getHexColor(): string
    {
        return match ($this) {
            self::PENDING => 'gray-600',
            self::SUCCESS => 'green-600',
            self::PARTIAL => 'blue-600',
            self::REVERSED => 'yellow-600',
            self::REFUNDED => 'yellow-600',
            self::CANCELLED => 'red-600',
            self::FAILED => 'red-600',
        };
    }
}
