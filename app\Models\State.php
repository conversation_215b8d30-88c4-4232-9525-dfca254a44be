<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class State extends Model
{
    /**
     * Get all of the localGovernmentAreas for the State
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function localGovernmentAreas(): HasMany
    {
        return $this->hasMany(LocalGovernmentArea::class);
    }
}
