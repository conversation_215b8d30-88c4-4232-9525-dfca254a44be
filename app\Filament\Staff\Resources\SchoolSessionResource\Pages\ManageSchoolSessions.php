<?php

namespace App\Filament\Staff\Resources\SchoolSessionResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\SchoolSessionResource;

class ManageSchoolSessions extends ManageRecords
{
    protected static string $resource = SchoolSessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->successNotification(function () {
                    return Notification::make()
                        ->success()
                        ->title('Level Created')
                        ->body('The level has been created successfully');
                }),
        ];
    }
}
