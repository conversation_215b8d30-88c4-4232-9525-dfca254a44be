<?php

namespace App\Filament\Staff\Resources\ProgrammeResource\Pages;

use Filament\Actions;
use App\Models\Department;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\ProgrammeResource;

class ManageProgrammes extends ManageRecords
{
    protected static string $resource = ProgrammeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    $data['school_id'] = Department::find($data['first_department_id'])->school_id;
                    return $data;
                })
                ->successNotification(function () {
                    return Notification::make()
                        ->success()
                        ->title('Programme Created')
                        ->body('The programme has been created successfully');
                }),
        ];
    }
}
