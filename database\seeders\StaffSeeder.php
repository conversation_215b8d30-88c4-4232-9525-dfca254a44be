<?php

namespace Database\Seeders;

use App\Enums\AddressState;
use App\Enums\Qualification;
use App\Enums\Title;
use App\Enums\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class StaffSeeder extends Seeder
{
    public function run()
    {
        User::create([
            'title' => Title::MR,
            'last_name' => 'Hamzat',
            'first_name' => 'Mikail',
            'middle_name' => 'Olatunji',
            'qualification' => [Qualification::BTECH],
            'phone' => '08135501693',
            'email' => '<EMAIL>',
            'role' => Role::ICT,
            'address_line' => 'Osolo Street',
            'address_town' => 'Obaagun',
            'address_state' => AddressState::OSUN,
            'password' => Hash::make('Homa.4662'),
        ]);

        User::create([
            'title' => Title::MRS,
            'last_name' => 'Hamzat',
            'first_name' => 'R.',
            'middle_name' => 'B.',
            'qualification' =>  [Qualification::MPA, Qualification::BA],
            'phone' => '08012345678',
            'email' => '<EMAIL>',
            'role' => Role::REGISTRAR,
            'address_line' => 'College Road',
            'address_town' => 'Ila-Orangun',
            'address_state' => AddressState::OSUN,
            'password' => Hash::make('Registrar.1234'),
        ]);
    }
}
