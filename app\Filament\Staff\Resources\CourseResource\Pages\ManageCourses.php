<?php

namespace App\Filament\Staff\Resources\CourseResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\CourseResource;

class ManageCourses extends ManageRecords
{
    protected static string $resource = CourseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->successNotification(function () {
                    return Notification::make()
                        ->success()
                        ->title('Course Created')
                        ->body('The course has been created successfully');
                }),
        ];
    }
}
