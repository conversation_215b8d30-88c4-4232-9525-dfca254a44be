<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum Qualification: string implements HasLabel
{
    case NCE = 'nce';
    case ND = 'nd';
    case HND = 'hnd';
    case BSC = 'bsc';
    case BA = 'ba';
    case BTECH = 'btech';
    case PGD = 'pgd';
    case MSC = 'msc';
    case MTECH = 'mtech';
    case MA = 'ma';
    case MPA = 'mpa';
    case PHD = 'phd';

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::NCE => 'NCE',
            self::ND => 'ND',
            self::HND => 'HND',
            self::BSC => 'B.Sc.',
            self::BA => 'B.A.',
            self::BTECH => 'B.Tech.',
            self::PGD => 'PGD',
            self::MSC => 'M.Sc.',
            self::MTECH => 'M.Tech.',
            self::MA => 'M.A.',
            self::MPA => 'M.P.A.',
            self::PHD => 'Ph.D.',
        };
    }
}
