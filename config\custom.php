<?php

return [

    /*
    |---------------------------------------------------------------------------
    | Portal Application URL
    |---------------------------------------------------------------------------
    |
    | The URL for the portal section of the application. This is used
    | to generate links and ensure proper routing for portal-related tasks.
    | Update this to match your portal's deployed URL.
    |
    */
    'portal_url' => env('PORTAL_URL', 'http://localhost/portal'),


    /*
    |--------------------------------------------------------------------------
    | Admin Email Configuration
    |--------------------------------------------------------------------------
    |
    | This value defines the email address of the application's administrator.
    | It is used for restricting access to certain functionalities or panels.
    | Ensure this value is correctly set in your environment file (.env).
    |
    */

    'admin_email' => env('ADMIN_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------- 
    | Application Email 
    |--------------------------------------------------------------------------- 
    |
    | This is the email for app notifications. 
    | Set it in the .env file to ensure correct configuration. 
    | This will be used for all outgoing app mails. 
    */
    'app_email' => env('APP_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------- 
    | Developer Email 
    |--------------------------------------------------------------------------- 
    |
    | This is the email for developer notifications. 
    | Set it in the .env file to ensure correct configuration. 
    | This will be used for all outgoing developer emails. 
    */
    'developer_email' => env('DEVELOPER_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------- 
    | ICT/Helpdesk Email 
    |--------------------------------------------------------------------------- 
    |
    | This is the email for developer notifications. 
    | Set it in the .env file to ensure correct configuration. 
    | This will be used for all outgoing developer emails. 
    */
    'ict_email' => env('ICT_EMAIL', '<EMAIL>'),


    /*
    |--------------------------------------------------------------------------- 
    | Application Phone
    |--------------------------------------------------------------------------- 
    |
    | This is the phone number for app notifications. 
    | Set it in the .env file to ensure correct configuration. 
    | This will be used for all outgoing calls/sms.
    */
    'app_phone' => env('APP_PHONE', '+2347067689462'),

    /*
    |--------------------------------------------------------------------------- 
    | ICT/Helpdesk Phone
    |--------------------------------------------------------------------------- 
    |
    | This is the phone number for app notifications. 
    | Set it in the .env file to ensure correct configuration. 
    | This will be used for all outgoing calls/sms.
    */
    'ict_phone' => env('ICT_PHONE', '+2347067689462'),

    /*
    |---------------------------------------------------------------------------
    | Paystack Configuration
    |---------------------------------------------------------------------------
    |
    | These keys and URLs are required for integrating Paystack's payment services
    | into your application. Ensure you set the appropriate values in your
    | environment file (.env) for a successful integration.
    |
    */
    'api_url' => getenv('PAYSTACK_API_URL'),
    'merchant_mail' => getenv('PAYSTACK_MERCHANT_EMAIL'),
    'public_key' => getenv('PAYSTACK_PUBLIC_KEY'),
    'secret_key' => getenv('PAYSTACK_SECRET_KEY'),

];
