<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\PaystackService;
use App\Jobs\ProcessChargeSuccess;
use Illuminate\Support\Facades\Log;

class EventWebhookController extends Controller
{
    public function handleEvent(Request $request)
    {
        try {
            // For test (logs raw payload)
            // $payload = json_decode($request->getContent(), true);
            // Log::info('Test Webhook Received: ', [$payload]);

            $payload = json_decode(app(PaystackService::class)->verifyPaystackSignature($request), true);

            if (!isset($payload['event'])) {
                Log::error('Invalid payload', ['payload' => $payload]);
                return response()->json(['message' => 'Invalid payload'], 400);
            }

            $event = $payload['event'];

            if ($event === 'charge.success') {
                ProcessChargeSuccess::dispatch($payload);
            }

            Log::info('Paystack Webhook Received: ' . $payload['data']['reference']);

            return response()->json(['message' => 'Webhook received and processed successfully'], 200);
        } catch (\Exception $e) {
            Log::error('Paystack Webhook Handling Failed', [
                'error' => $e->getMessage(),
                'payload' => $request->getContent(),
            ]);

            return response()->json(['message' => 'Error handling webhook'], 500);
        }
    }
}
