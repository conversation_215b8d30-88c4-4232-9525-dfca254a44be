<?php

namespace App\Filament\Staff\Clusters\Settings\Pages;

use Closure;
use App\Enums\Role;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use App\Settings\AdmissionSettings;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Filament\Staff\Clusters\Settings;
use Filament\Forms\Components\FileUpload;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;


class ManageAdmission extends SettingsPage
{
    protected static string $settings = AdmissionSettings::class;
    protected static ?string $cluster = Settings::class;
    protected static ?string $navigationLabel = 'Admission';

    public static function canAccess(): bool
    {
        return management_staff_access();
    }
    protected function isDisabled(): bool
    {
        return !in_array(Auth::user()->role, [Role::ICT, Role::REGISTRAR]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('screening')
                            ->label('Screening')
                            ->schema([
                                TextInput::make('screening_max_score')
                                    ->disabled($this->isDisabled())
                                    ->label('Max score')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(400)
                                    ->extraAttributes(['style' => 'width: 80px;']),
                                TextInput::make('screening_cut_off_mark')
                                    ->disabled($this->isDisabled())
                                    ->label('Cut-off mark')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(400)
                                    ->extraAttributes(['style' => 'width: 80px;'])
                                    ->rule(function (callable $get) {
                                        return function (string $attribute, $value, Closure $fail) use ($get) {
                                            $totalScore = $get('screening_max_score');
                                            if ($value >= $totalScore) {
                                                $fail('Cut-off mark must be less than maximum score.');
                                            }
                                        };
                                    }),
                            ]),
                        Tabs\Tab::make('requirements')
                            ->label('Requirements')
                            ->schema([
                                FileUpload::make('fee_schedule')
                                    ->disabled($this->isDisabled())
                                    ->downloadable()
                                    ->directory('documents')
                                    ->maxSize(512)
                                    ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                                    ->getUploadedFileNameForStorageUsing(
                                        function (TemporaryUploadedFile $file): string {
                                            $extension = $file->getClientOriginalExtension();
                                            return 'fee_schedule-' . now()->format('d-m-Y-H-i-s') . '.' . $extension;
                                        }
                                    )

                            ]),
                        // Tabs\Tab::make('Application')
                        //     ->schema([
                        //         Section::make('Application fee')                                        
                        //             ->description(new HtmlString(
                        //                 'The application fee is the amount that applicants are required to pay to apply for admission to the school. 
                        //             <br><b>NOTE:</b> Only the staffistrator can change this value.'
                        //             ))
                        //             ->schema([
                        //                 TextInput::make('application_fee')
                        //                     // ->disabled($this->isDisabled())
                        //                     ->label('Amount')
                        //                     ->required()
                        //                     ->numeric()
                        //                     ->prefix('₦')
                        //                     ->minValue(500)
                        //                     ->maxValue(100000)
                        //                     ->extraAttributes(['class' => '!min-w-32 !w-32']),
                        //             ]),

                        // ])->columns(2),
                    ]),
            ]);
    }

    public function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Settings Updated')
            ->body('The admission settings have been saved successfully.');
    }
}
