@php
    use App\Services\AcademicCalendarService;
    use App\Settings\CollegeSettings;

    $calendar = app(AcademicCalendarService::class);

    $panelId = filament()->getCurrentPanel()?->getId();
    $activeSession = $calendar->getCurrentSession();
    $activeSemester = $calendar->getActiveSemesterText();
@endphp

@if ($panelId !== 'help')
<div class="hidden lg:flex flex-col text-xs text-gray-600 dark:text-gray-200">
    <a href="{{ config('app.url') }}" class="font-semibold mb-2">
        {{ app(CollegeSettings::class)->name }}
    </a>
    <div>
        <span class="font-semibold">Session:</span>
        <span>{{ $activeSession }} |</span>
        <span class="font-semibold">Semester:</span>
        <span>{{ $activeSemester }}</span>
    </div>
</div>
@endif
