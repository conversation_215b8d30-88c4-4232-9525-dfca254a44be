<?php

namespace Database\Seeders;

use App\Models\SemesterSchedule;
use Illuminate\Database\Seeder;

class SemesterScheduleSeeder extends Seeder
{
    public function run()
    {
        SemesterSchedule::insert([
            // 2022/2023
            ['school_session_id' => 1, 'semester_id' => 1, 'semester_start' => '2022-09-01 00:00:00', 'semester_end' => '2022-12-31 23:59:59'],
            ['school_session_id' => 1, 'semester_id' => 2, 'semester_start' => '2023-01-01 00:00:00', 'semester_end' => '2023-06-30 23:59:59'],

            // 2023/2024
            ['school_session_id' => 2, 'semester_id' => 1, 'semester_start' => '2023-09-01 00:00:00', 'semester_end' => '2023-12-31 23:59:59'],
            ['school_session_id' => 2, 'semester_id' => 2, 'semester_start' => '2024-01-01 00:00:00', 'semester_end' => '2024-06-30 23:59:59'],

            // 2024/2025
            ['school_session_id' => 3, 'semester_id' => 1, 'semester_start' => '2024-09-01 00:00:00', 'semester_end' => '2025-03-31 23:59:59'],
            ['school_session_id' => 3, 'semester_id' => 2, 'semester_start' => '2025-04-30 00:00:00', 'semester_end' => '2025-08-30 23:59:59'],

            // 2025/2026
            ['school_session_id' => 4, 'semester_id' => 1, 'semester_start' => '2025-09-01 00:00:00', 'semester_end' => '2026-03-31 23:59:59'],
            ['school_session_id' => 4, 'semester_id' => 2, 'semester_start' => '2026-04-30 00:00:00', 'semester_end' => '2026-08-30 23:59:59'],
        ]);
    }
}
