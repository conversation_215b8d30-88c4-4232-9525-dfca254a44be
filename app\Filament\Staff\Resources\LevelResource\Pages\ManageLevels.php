<?php

namespace App\Filament\Staff\Resources\LevelResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\LevelResource;

class ManageLevels extends ManageRecords
{
    protected static string $resource = LevelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->successNotification(function () {
            //         return Notification::make()
            //             ->success()
            //             ->title('Level Created')
            //             ->body('The level has been created successfully');
            //     }),
        ];
    }
}
