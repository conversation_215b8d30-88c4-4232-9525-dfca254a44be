<?php

namespace App\Filament\Staff\Resources\OverviewResource\Pages;

use App\Filament\Staff\Resources\OverviewResource;
use Filament\Resources\Pages\ManageRecords;

class ManageOverview extends ManageRecords
{
    protected static string $resource = OverviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    // public function updatedTableFilters(): void
    // {
    //     $this->resetTable();
    // }
}
