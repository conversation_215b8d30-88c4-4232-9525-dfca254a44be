<?php

namespace App\Http\Controllers;

use App\Models\Registration;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;

class RegistrationController extends Controller
{

    public function print(Registration $registration)
    {
        return view('filament.documents.registration', $this->getRegistrationData($registration));
    }

    public function download(Registration $registration)
    {
        return Pdf::view('filament.documents.registration', $this->getRegistrationData($registration))
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name('Registration - ' . $registration->user->name . '.pdf')
            ->download();
    }

    private function getRegistrationData(Registration $registration)
    {
        $collegeSettings = app(CollegeSettings::class);

        $student = $registration->user;

        $headOfDepartment = $registration->programme->firstDepartment->headOfDepartment;

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
            'registration' => $registration,
            'headOfDepartment' => $headOfDepartment
        ];
    }
}
