<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class SchoolSession extends Model
{
    use SoftDeletes;

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get all of the registrations for the SchoolSession
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    // public function registrations(): HasMany
    // {
    //     // return $this->hasMany(Registration::class);
    // }

    /**
     * Get all of the semesterSchedules for the SchoolSession
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function semesterSchedules(): HasMany
    {
        return $this->hasMany(SemesterSchedule::class);
    }

    /**
     * Get all of the semesters for the SchoolSession
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function semesters(): HasManyThrough
    {
        return $this->hasManyThrough(
            Semester::class,           // Final model you want (Semesters)
            SemesterSchedule::class,   // Intermediate model (pivot)
            'school_session_id',       // Foreign key on SemesterSchedule that points to SchoolSession
            'id',                      // Local key on Semester model (usually 'id')
            'id',                      // Local key on SchoolSession model (usually 'id')
            'semester_id'              // Foreign key on SemesterSchedule that points to Semester
        );
    }
}
