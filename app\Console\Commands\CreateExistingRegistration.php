<?php

namespace App\Console\Commands;

use App\Enums\Role;
use App\Models\User;
use App\Enums\FeeType;
use App\Enums\InvoiceStatus;
use App\Models\Registration;
use Illuminate\Console\Command;
use App\Services\CodeGenerationService;

class CreateExistingRegistration extends Command
{
    protected $signature = 'registration:create-existing';
    protected $description = 'Create existing registration for students';

    public function handle()
    {
        $students = User::where('role', Role::STUDENT)
            ->whereHas('registrations', function ($query) {
                $query->where('level_id', 3);
            })->get();

        $portalFee = 0;

        foreach ($students as $student) {
            $existing = $student->registrations()->first();
            if (! $existing) {
                $this->warn("No registration found for student ID {$student->id}, skipping.");
                continue;
            }

            $programmeId = $existing->programme_id;

            foreach ([1, 2] as $sessionId) {
                foreach ([1 => 'First', 2 => 'Second'] as $semesterId => $label) {
                    $levelId = $sessionId == 2 ? 2 : 1;

                    $alreadyExists = $student->registrations()->where([
                        'school_session_id' => $sessionId,
                        'semester_id' => $semesterId,
                        'level_id' => $levelId,
                    ])->exists();

                    if ($alreadyExists) {
                        $this->warn("Registration for student ID {$student->id}, Session {$sessionId}, Semester {$label}, Level {$levelId} already exists. Skipping.");
                        continue;
                    }

                    $registration = $student->registrations()->create([
                        'school_session_id' => $sessionId,
                        'semester_id' => $semesterId,
                        'level_id' => $levelId,
                        'programme_id' => $programmeId,
                    ]);

                    $registration->portalInvoice()->create([
                        'user_id' => $student->id,
                        'payable_id' => $registration->id,
                        'payable_type' => Registration::class,
                        'number' => app(CodeGenerationService::class)->generateInvoiceNumber(),
                        'reference' => app(CodeGenerationService::class)->generateReference(),
                        'description' => [
                            [
                                'item' => "Portal access",
                                'amount' => $portalFee,
                            ]
                        ],
                        'total_amount' => $portalFee,
                        'fee_type' => FeeType::PORTAL,
                        'invoice_status' => InvoiceStatus::PAID,
                        'paid_at' => now(),
                    ]);
                }
            }
        }

        $this->info('Existing registrations for NCE 3 created successfully!');
    }
}
