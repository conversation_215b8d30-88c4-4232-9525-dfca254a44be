<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum GuardianOccupation: string implements Has<PERSON>abel
{
    case ACCOUNTANT = 'accountant';
    case ARTISAN = 'artisan';
    case BANKER = 'banker';
    case BUSINESSPERSON = 'businessperson';
    case CIVIL_SERVANT = 'civil_servant';
    case DOCTOR = 'doctor';
    case ELECTRICIAN = 'electrician';
    case ENGINEER = 'engineer';
    case ENTREPRENEUR = 'entrepreneur';
    case FARMER = 'farmer';
    case LAWYER = 'lawyer';
    case LECTURER = 'lecturer';
    case NURSE = 'nurse';
    case TEACHER = 'teacher';
    case TECHNICIAN = 'technician';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::ACCOUNTANT => 'Accountant',
            self::ARTISAN => 'Artisan',
            self::BANKER => 'Banker',
            self::BUSINESSPERSON => 'Businessperson',
            self::CIVIL_SERVANT => 'Civil Servant',
            self::DOCTOR => 'Doctor',
            self::ELECTRICIAN => 'Electrician',
            self::ENGINEER => 'Engineer',
            self::ENTREPRENEUR => 'Entrepreneur',
            self::FARMER => 'Farmer',
            self::LAWYER => 'Lawyer',
            self::LECTURER => 'Lecturer',
            self::NURSE => 'Nurse',
            self::TEACHER => 'Teacher',
            self::TECHNICIAN => 'Technician',
        };
    }
}
