<?php

namespace App\Settings;

// use App\Casts\SettingsMoneyCast;
use <PERSON><PERSON>\LaravelSettings\Settings;

class AdmissionSettings extends Settings
{
    public int $screening_max_score;
    public int $screening_cut_off_mark;
    // public float $application_fee;
    public ?string $fee_schedule = null;

    public static function group(): string
    {
        return 'admission';
    }

    public static function casts(): array
    {
        return [
            // 'application_fee' => SettingsMoneyCast::class,
        ];
    }

    public static function default(): array
    {
        return [
            'screening_max_score' => 100,
            'screening_cut_off_mark' => 50,
            // 'application_fee' => 100000,
            'fee_schedule' => null,
        ];
    }
}
