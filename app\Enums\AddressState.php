<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum AddressState: string implements HasLabel

{
    case ABIA = 'abia';
    case ADAMAWA = 'adamawa';
    case AKWA_IBOM = 'akwa_ibom';
    case ANAMBRA = 'anambra';
    case BAUCHI = 'bauchi';
    case BAYELSA = 'bayelsa';
    case BENUE = 'benue';
    case BORNO = 'borno';
    case CROSS_RIVER = 'cross_river';
    case DELTA = 'delta';
    case EBONYI = 'ebonyi';
    case EDO = 'edo';
    case EKITI = 'ekiti';
    case ENUGU = 'enugu';
    case GOMBE = 'gombe';
    case IMO = 'imo';
    case JIGAWA = 'jigawa';
    case KADUNA = 'kaduna';
    case KANO = 'kano';
    case KATSINA = 'katsina';
    case KEBBI = 'kebbi';
    case KOGI = 'kogi';
    case KWARA = 'kwara';
    case LAGOS = 'lagos';
    case NASARAWA = 'nasarawa';
    case NIGER = 'niger';
    case OGUN = 'ogun';
    case ONDO = 'ondo';
    case OSUN = 'osun';
    case OYO = 'oyo';
    case PLATEAU = 'plateau';
    case RIVERS = 'rivers';
    case SOKOTO = 'sokoto';
    case TARABA = 'taraba';
    case YOBE = 'yobe';
    case ZAMFARA = 'zamfara';
    case FCT = 'fct';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::ABIA => 'Abia',
            self::ADAMAWA => 'Adamawa',
            self::AKWA_IBOM => 'Akwa Ibom',
            self::ANAMBRA => 'Anambra',
            self::BAUCHI => 'Bauchi',
            self::BAYELSA => 'Bayelsa',
            self::BENUE => 'Benue',
            self::BORNO => 'Borno',
            self::CROSS_RIVER => 'Cross River',
            self::DELTA => 'Delta',
            self::EBONYI => 'Ebonyi',
            self::EDO => 'Edo',
            self::EKITI => 'Ekiti',
            self::ENUGU => 'Enugu',
            self::GOMBE => 'Gombe',
            self::IMO => 'Imo',
            self::JIGAWA => 'Jigawa',
            self::KADUNA => 'Kaduna',
            self::KANO => 'Kano',
            self::KATSINA => 'Katsina',
            self::KEBBI => 'Kebbi',
            self::KOGI => 'Kogi',
            self::KWARA => 'Kwara',
            self::LAGOS => 'Lagos',
            self::NASARAWA => 'Nasarawa',
            self::NIGER => 'Niger',
            self::OGUN => 'Ogun',
            self::ONDO => 'Ondo',
            self::OSUN => 'Osun',
            self::OYO => 'Oyo',
            self::PLATEAU => 'Plateau',
            self::RIVERS => 'Rivers',
            self::SOKOTO => 'Sokoto',
            self::TARABA => 'Taraba',
            self::YOBE => 'Yobe',
            self::ZAMFARA => 'Zamfara',
            self::FCT => 'FCT',
        };
    }
}
