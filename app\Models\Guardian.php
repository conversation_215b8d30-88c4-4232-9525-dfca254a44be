<?php

namespace App\Models;

use Illuminate\Support\Str;
use App\Enums\Title;
use App\Enums\GuardianOccupation;
use App\Enums\GuardianRelationship;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Guardian extends Model
{
    protected $casts = [
        'title' => Title::class,
        'relationship' => GuardianRelationship::class,
        'occupation' => GuardianOccupation::class,
    ];

    public function name(): Attribute
    {
        return Attribute::get(
            fn() => collect([$this->last_name, $this->first_name])
                ->filter()
                ->values()
                ->map(fn($part, $key) => $key === 0 ? Str::upper($part) : Str::ucfirst($part))
                ->join(' ')
        );
    }


    /**
     * Get the user that owns the Guardian
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
