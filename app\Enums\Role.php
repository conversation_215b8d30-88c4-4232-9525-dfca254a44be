<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum Role: int implements HasLabel
{
    case STUDENT = 1;
    case PROPRIETOR = 2;
    case PROVOST = 3;
    case DEPUTY_PROVOST = 4;
    case REGISTRAR = 5;
    case BURSAR = 6;
    case LIBRARIAN = 7;
    case EXAMINER = 8;
    case ICT = 9;
    case ADMIN = 10;
    case HOD = 11;

    public function getLabel(): string
    {
        return match ($this) {
            self::STUDENT => 'Student',
            self::PROPRIETOR => 'Proprietor',
            self::PROVOST => 'Provost',
            self::DEPUTY_PROVOST => 'Deputy Provost',
            self::REGISTRAR => 'Ag. Registrar',
            self::BURSAR => 'Bursar',
            self::LIBRARIAN => 'Librarian',
            self::EXAMINER => 'Examiner',
            self::ICT => 'ICT',
            self::ADMIN => 'Admin',
            self::HOD => 'H.O.D'
        };
    }
}
