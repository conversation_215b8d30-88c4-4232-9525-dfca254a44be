<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class News extends Model
{
    public function title(): Attribute
    {
        return Attribute::get(fn($value) => Str::title($value));
    }
    protected $table = 'news';

    protected $casts = [
        'date' => 'date',
        'is_published' => 'boolean'
    ];
}
