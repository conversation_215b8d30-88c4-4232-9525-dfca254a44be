<?php

namespace App\Filament\Staff\Resources\DepartmentResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Resources\DepartmentResource;

class ManageDepartments extends ManageRecords
{
    protected static string $resource = DepartmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->successNotification(function () {
                    return Notification::make()
                        ->success()
                        ->title('Department Created')
                        ->body('The department has been created successfully');
                }),
        ];
    }
}
