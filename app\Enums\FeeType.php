<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum FeeType: int implements HasLabel
{
    case PORTAL = 1;
    case REGISTRATION = 2;
    case MISCELLANEOUS = 3;


    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::PORTAL => 'Portal',
            self::REGISTRATION => 'Registration',
            self::MISCELLANEOUS => 'Miscellaneous',
        };
    }
}
