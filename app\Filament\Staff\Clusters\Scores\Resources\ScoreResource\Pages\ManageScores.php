<?php

namespace App\Filament\Staff\Clusters\Scores\Resources\ScoreResource\Pages;

use App\Models\Grade;
use App\Models\Score;
use App\Models\Assessment;
use Illuminate\Support\Str;
use Filament\Actions\Action;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Cache;
use Filament\Tables\Actions\ActionGroup;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Clusters\Scores\Resources\ScoreResource;

class ManageScores extends ManageRecords
{
    protected static string $resource = ScoreResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                $this->generateExportGroup('BlankScoresheet')
                    ->extraAttributes(['style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important; margin-top: 0.5rem !important;',]),
                $this->generateExportGroup('FilledScoresheet')
                    ->extraAttributes(['style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',]),
            ])
                ->button()
                ->label('Export scoresheets')
                ->icon('heroicon-m-document-text'),
        ];
    }

    private function generateExportGroup(string $type): ActionGroup
    {
        return ActionGroup::make([
            $this->generateExportAction("print_$type", 'heroicon-o-printer', $type),
            $this->generateExportAction("download_$type", 'heroicon-o-document-arrow-down', $type),
        ])
            ->link()
            ->icon(false)
            ->label(Str::replace('Scoresheet', ' scoresheet', $type));
    }

    private function generateExportAction(string $actionType, string $icon, string $mode = 'FilledScoresheet'): Action
    {
        return Action::make($actionType)
            ->disabled(fn() => $this->hasNoRequiredFiltersNorFilteredRecords())
            ->icon($icon)
            ->label(Str::contains($actionType, 'print') ? 'Print' : 'Download')
            ->action(function () use ($actionType, $mode) {
                $tableFilters = $this->extractFilters();
                $filteredRecords = $this->getFilteredTableQuery()->get();

                $scoreData = [
                    'students' => $filteredRecords
                        ->sortBy('last_name')
                        ->values()
                        ->map(fn($student) => [
                            'id' => $student->id,
                            'name' => $student->name,
                            'matric_number' => $student->matric_number,
                            // 'scores' => $mode === 'BlankScoresheet' ? null : $this->getScores($student),
                            'assessmentScores' => $mode === 'BlankScoresheet' ? null : $this->getAssessmentScores($student),
                            'totalScore' => $mode === 'BlankScoresheet' ? null : $this->getTotalScore($student),
                            'grade' => $mode === 'BlankScoresheet' ? null : $this->getGrade($student),
                        ]),
                    'tableFilters' => $tableFilters,
                    'assessments' => Assessment::select('id', 'name', 'max_score')->orderBy('order')->get(),
                    'blank' => $mode === 'BlankScoresheet',
                ];

                $cacheKey = 'scoreData_' . uniqid();
                Cache::put($cacheKey, $scoreData, now()->addMinutes(5));

                // strip "print_" or "download_" from actionType
                $baseAction = Str::contains($actionType, 'print') ? 'print' : 'download';

                $url = URL::signedRoute("scoresheet.$baseAction", ['cacheKey' => $cacheKey]);

                if ($baseAction === 'print') {
                    $this->js("(function() {
                        const newWindow = window.open(
                            '$url',
                            'scores',
                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                        );

                        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                            alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                        } else {
                            newWindow.focus();
                        }
                    })();");
                } else {
                    $this->js("window.location.href = '$url';");
                }
            });
    }

    private function hasNoRequiredFiltersNorFilteredRecords(): bool
    {

        if (!$this->hasRequiredFilters()) {
            return true;
        }

        return $this->getFilteredRecords()->isEmpty();
    }

    private function getFilteredRecords()
    {
        return $this->getFilteredTableQuery()->get();
    }

    private function hasRequiredFilters(): bool
    {
        $filters = $this->extractFilters();

        return isset(
            $filters['school_session_id'],
            $filters['semester_id'],
            $filters['level_id'],
            $filters['course_id']
        );
    }

    private function extractFilters(): array
    {
        $filters = $this->tableFilters['score_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
            'course_id' => $filters['course_id'] ?? null,
        ];
    }

    private function getAssessmentScores($student): array
    {
        $filters = $this->extractFilters();
        $registration = ScoreResource::getRegistration($student->id, $filters);

        if (!$registration) {
            return [];
        }

        return Score::where([
            'registration_id' => $registration->id,
            'course_id' => $filters['course_id'],
        ])->with('assessment')->get()->mapWithKeys(fn($score) => [
            $score->assessment->name => $score->score
        ])->toArray();
    }

    private function getTotalScore($student): ?int
    {
        $filters = $this->extractFilters();
        return ScoreResource::getTotalScore($student, $filters);
    }

    private function getGrade($student): ?string
    {
        $totalScore = $this->getTotalScore($student);
        if (!$totalScore) return null;

        $grades = Grade::get();
        $grade = ScoreResource::getGrade($grades, $totalScore);
        return $grade?->name;
    }
}
