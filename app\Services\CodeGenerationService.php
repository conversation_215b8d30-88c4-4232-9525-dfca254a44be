<?php

namespace App\Services;

use App\Models\User;
use App\Models\Programme;
use App\Models\SchoolSession;
use App\Models\InvoiceCounter;
use App\Models\AdmissionCounter;
use App\Models\ApplicationCounter;
use App\Models\TransactionCounter;

class CodeGenerationService
{
    public function generateReference(): string
    {
        return 'REF' . now()->format('YmdHis') . '-' . strtoupper(uniqid());
    }

    public function generateInvoiceNumber(): string
    {
        $todayDate = now()->format('dmY');

        $nextSequence = InvoiceCounter::getNextSequence();

        return 'CPAY' . $todayDate . self::formatSequence($nextSequence);
    }

    public function generateTransactionNumber(): string
    {
        $todayDate = now()->format('dmY');

        $nextSequence = TransactionCounter::getNextSequence();

        return 'TXN' . $todayDate . self::formatSequence($nextSequence);
    }

    public function generateMatriculationNumber($schoolSessionId, $programmeId): string
    {
        $nextSequence = AdmissionCounter::getNextSequence();
        $entryYear = substr(explode('/', SchoolSession::find($schoolSessionId)->name)[0], -2);
        $programme = Programme::with(['firstDepartment', 'secondDepartment'])->findOrFail($programmeId);

        $first = $programme->firstDepartment?->code;
        $second = $programme->secondDepartment?->code;

        $programmeAlias = collect([$first, $second])->filter()->join('-');


        return 'RACOED/' . $entryYear . '/' . $programmeAlias . '/' . self::formatSequence($nextSequence);
    }

    public function generateApplicationNumber(): string
    {
        $nextSequence = ApplicationCounter::getNextSequence();

        $date = now()->format('dmY');

        return 'APP/' . $date . '/' . self::formatSequence($nextSequence);
    }

    private static function formatSequence(int $sequence): string
    {
        return str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }
}
