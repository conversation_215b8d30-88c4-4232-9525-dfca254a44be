<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum GuardianRelationship: string implements <PERSON><PERSON><PERSON><PERSON>
{
    case AUNT = 'aunt';
    case BROTHER = 'brother';
    case COUSIN = 'cousin';
    case FATHER = 'father';
    case G<PERSON>NDFATHER = 'grandfather';
    case G<PERSON><PERSON>MOTHER = 'grandmother';
    case GUARDIAN = 'guardian';
    case MOTHER = 'mother';
    case SISTER = 'sister';
    case UNCLE = 'uncle';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::AUNT => 'Aunt',
            self::BROTHER => 'Brother',
            self::COUSIN => 'Cousin',
            self::FATHER => 'Father',
            self::G<PERSON>NDFATHER => 'Grandfather',
            self::G<PERSON><PERSON>MOTHER => 'Grandmother',
            self::GUARDIAN => 'Guardian',
            self::MOTHER => 'Mother',
            self::SISTER => 'Sister',
            self::UNCLE => 'Uncle',
        };
    }
}
