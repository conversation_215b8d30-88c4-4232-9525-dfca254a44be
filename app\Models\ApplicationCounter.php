<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class ApplicationCounter extends Model
{
    public static function getNextSequence(): int
    {
        return DB::transaction(function () {
            $counter = self::lockForUpdate()->first();

            if (!$counter) {
                $counter = self::create([
                    'last_sequence' => 1,
                ]);
                return 1;
            }

            $counter->increment('last_sequence');
            return $counter->last_sequence;
        });
    }
}
