<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum Title: string implements HasLabel

{
    case ALHAJI = 'alhaji';
    case CHIEF = 'chief';
    case DR = 'dr';
    case ENGR = 'engr';
    case HON = 'hon';
    case MALLAM = 'mallam';
    case MR = 'mr';
    case PASTOR = 'pastor';
    case PROF = 'prof';
    case MISS = 'miss';
    case MRS = 'mrs';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::ALHAJI => 'Alhaji',
            self::CHIEF => 'Chief',
            self::DR => 'Dr.',
            self::ENGR => 'Engr.',
            self::HON => 'Hon.',
            self::MALLAM => 'Mallam',
            self::MR => 'Mr.',
            self::PASTOR => 'Pastor',
            self::PROF => 'Prof.',
            self::MISS => 'Miss',
            self::MRS => 'Mrs.',
        };
    }
}
