<?php

namespace App\Livewire;

use Livewire\Component;
use Filament\Forms\Form;
use Filament\Forms\Components\Grid;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Concerns\InteractsWithForms;

class Contact extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                    ->schema([
                        TextInput::make('name')
                            ->label('Full name')
                            ->placeholder('Mikai<PERSON> Hamzat')
                            ->required()
                            ->maxLength(50),

                        TextInput::make('email')
                            ->label('Email address')
                            ->placeholder('<EMAIL>')
                            ->email()
                            ->required()
                            ->maxLength(50),

                        TextInput::make('phone')
                            ->placeholder('07067689462')
                            ->label('Phone')
                            ->required()
                            ->tel(),

                        TextInput::make('subject')
                            ->label('Subject')
                            ->placeholder('Admission Enquiry')
                            ->required()
                            ->maxLength(50),

                        Textarea::make('message')
                            ->placeholder('Your message here...')
                            ->label('Message')
                            ->maxLength(255)
                            ->required()
                            ->rows(4)
                            ->columnSpan(['default' => 1, 'md' => 2,]),
                    ]),
            ])
            ->statePath('data');
    }

    public function submit(): void
    {
        $data = $this->form->getState();

        // Handle form submission
        // TODO:Example: Mail::to('<EMAIL>')->send(new ContactFormMail($data));

        // Reset form
        $this->form->fill();

        // Show success notification
        Notification::make()
            ->title('Message not sent!')
            ->body('Please contact the school via phone or email.')
            ->danger()
            ->send();
    }

    public function render()
    {
        return view('livewire.pages.contact');
    }
}
