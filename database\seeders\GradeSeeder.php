<?php

namespace Database\Seeders;

use App\Models\Grade;
use Illuminate\Database\Seeder;

class GradeSeeder extends Seeder
{
    public function run()
    {
        Grade::insert([
            ['name' => 'A', 'remark' => 'Distinction', 'min_score' => 70, 'max_score' => 100, 'point' => 5, 'min_point' => 4.50, 'max_point' => 5.00],
            ['name' => 'B', 'remark' => 'Credit', 'min_score' => 60, 'max_score' => 69, 'point' => 4, 'min_point' => 3.50, 'max_point' => 4.49],
            ['name' => 'C', 'remark' => 'Merit', 'min_score' => 50, 'max_score' => 59, 'point' => 3, 'min_point' => 2.40, 'max_point' => 3.49],
            ['name' => 'D', 'remark' => 'Pass', 'min_score' => 45, 'max_score' => 49, 'point' => 2, 'min_point' => 1.50, 'max_point' => 2.39],
            ['name' => 'E', 'remark' => 'Low Pass', 'min_score' => 40, 'max_score' => 44, 'point' => 1, 'min_point' => 1.00, 'max_point' => 1.49],
            ['name' => 'F', 'remark' => 'Fail', 'min_score' => 0, 'max_score' => 39, 'point' => 0, 'min_point' => 0.00, 'max_point' => 0.99],
        ]);
    }
}
