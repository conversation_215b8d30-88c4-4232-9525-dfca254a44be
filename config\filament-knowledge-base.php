<?php

// config for Guava/KnowledgeBasePanel

return [
    'panel' => [
        'id' => env('FILAMENT_KB_ID', 'help'),
        'path' => env('FILAMENT_KB_PATH', 'help'),
    ],

    'docs-path' => env('FILAMENT_KB_DOCS_PATH', 'help'),

    'model' => \Guava\FilamentKnowledgeBase\Models\FlatfileDocumentation::class,

    'cache' => [
        'prefix' => env('FILAMENT_KB_CACHE_PREFIX', 'filament_help_'),
        'ttl' => env('FILAMENT_KB_CACHE_TTL', 'forever'),
    ],
];
