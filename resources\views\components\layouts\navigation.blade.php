<nav x-data="{ open: false }" class="fixed top-0 inset-x-0 z-40 border-b border-gray-300 bg-white">
    {{-- Desktop Navigation --}}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">

            <div class="flex-shrink-0">
                <a href="{{ route('home') }}" wire:navigate>
                    {{-- The logo is now always visible for better brand presence on mobile --}}
                    <x-application-logo class="block h-20 w-auto" />
                </a>
            </div>

            <div class="hidden sm:flex sm:items-center sm:justify-center sm:space-x-8">
                <x-nav-link :href="route('home')" :active="request()->routeIs('home')" wire:navigate>
                    {{ __('Home') }}
                </x-nav-link>
                <x-nav-link :href="route('about')" :active="request()->routeIs('about')" wire:navigate>
                    {{ __('About us') }}
                </x-nav-link>

                {{-- Academics Dropdown --}}
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-nav-link  :active="request()->routeIs(['schools.*', 'result-checker'])">
                            {{ __('Academics') }} <x-filament::icon icon="heroicon-m-chevron-down" class="w-5 h-5 inline-block" />
                        </x-nav-link>
                    </x-slot>

                    {{-- Schools Dropdown --}}
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item>
                            <x-filament::dropdown placement="right-start" class="w-full">
                                <x-slot name="trigger">
                                    <div class="flex justify-between items-center w-full">
                                        <span>Schools</span>
                                        <x-filament::icon icon="heroicon-m-chevron-right" class="w-4 h-4 ml-2" />
                                    </div>
                                </x-slot>
                                <x-filament::dropdown.list>
                                    <x-filament::dropdown.list.item :href="route('schools.arts')" tag="a" wire:navigate>Arts & Social Sciences</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.education')" tag="a" wire:navigate>General Education</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.general')" tag="a" wire:navigate>General Studies Education</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.languages')" tag="a" wire:navigate>Languages</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.sciences')" tag="a" wire:navigate>Sciences</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.vocational')" tag="a" wire:navigate>Vocational & Technical</x-filament::dropdown.list.item>
                                </x-filament::dropdown.list>
                            </x-filament::dropdown>
                        </x-filament::dropdown.list.item>

                        {{-- Other Items --}}
                        {{-- <x-filament::dropdown.list.item :href="route('academic-calendar')" tag="a" wire:navigate>
                            Academic Calendar
                        </x-filament::dropdown.list.item> --}}
                        <x-filament::dropdown.list.item :href="route('result-checker')" tag="a" wire:navigate>
                            Result Checker
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>

                {{-- Admissions Dropdown --}}
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-nav-link  :active="request()->routeIs('nce-full-time')">
                            {{ __('Admissions') }} <x-filament::icon icon="heroicon-m-chevron-down" class="w-5 h-5 inline-block" />
                        </x-nav-link>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item :href="route('nce-full-time')" tag="a" wire:navigate>
                            NCE - Full Time
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>

                <x-nav-link :href="route('news.index')" :active="request()->routeIs('news.index')" wire:navigate>
                    {{ __('Campus News') }}
                </x-nav-link>
                <x-nav-link :href="route('contact')" :active="request()->routeIs('contact')" wire:navigate>
                    {{ __('Contact us') }}
                </x-nav-link>
            </div>

            <div class="hidden sm:flex sm:items-center sm:space-x-4">
                {{-- Login/Portal Link --}}
                @auth
                    <x-nav-link :href="config('custom.portal_url')">
                        {{ __('Portal') }}
                    </x-nav-link>
                @else
                    <x-nav-link :href="config('custom.portal_url')">
                        {{ __('Login') }}
                    </x-nav-link>
                @endauth

                {{-- CTA Dropdown --}}
                <x-filament::dropdown placement="bottom-end">
                    <x-slot name="trigger">
                        <x-filament::button icon="heroicon-o-academic-cap" color="primary">
                            APPLY NOW!
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item :href="route('nce-full-time')" tag="a" wire:navigate>
                            NCE - Full Time
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>

            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open"
                        class="inline-flex items-center justify-center p-2 rounded-sm text-black hover:text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-controls="responsive-menu"
                        :aria-expanded="open.toString()">
                    <span class="sr-only">Open main menu</span>
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

    </div>

    {{-- Responsive Navigation --}}
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden" id="responsive-menu">
        <div class="pt-2 pb-3 px-4 space-y-1">
            <div class="py-2">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button icon="heroicon-o-academic-cap" color="primary">
                            APPLY NOW!
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item :href="route('nce-full-time')" tag="a" wire:navigate>
                            NCE - Full Time
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>

            <x-responsive-nav-link :href="route('home')" :active="request()->routeIs('home')" wire:navigate>
                {{ __('Home') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('about')" :active="request()->routeIs('about')" wire:navigate>
                {{ __('About us') }}
            </x-responsive-nav-link>

            {{-- Responsive Academics Dropdown --}}
            <x-filament::dropdown placement="top-start">
                <x-slot name="trigger">
                    <x-responsive-nav-link  :active="request()->routeIs(['result-checker', 'schools.*'])">
                        {{ __('Academics') }} <x-filament::icon icon="heroicon-m-chevron-down" class="w-5 h-5 inline-block" />
                    </x-responsive-nav-link>
                </x-slot>
                <x-filament::dropdown.list>

                     {{-- Schools Dropdown --}}
                     <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item>
                            <x-filament::dropdown placement="right-start" class="w-full">
                                <x-slot name="trigger">
                                    <div class="flex justify-between items-center w-full">
                                        <span>Schools</span>
                                        <x-filament::icon icon="heroicon-m-chevron-right" class="w-4 h-4 ml-2" />
                                    </div>
                                </x-slot>
                                <x-filament::dropdown.list>
                                    <x-filament::dropdown.list.item :href="route('schools.arts')" tag="a" wire:navigate>Arts & Social Sciences</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.education')" tag="a" wire:navigate>Education</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.languages')" tag="a" wire:navigate>Languages</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.sciences')" tag="a" wire:navigate>Sciences</x-filament::dropdown.list.item>
                                    <x-filament::dropdown.list.item :href="route('schools.vocational')" tag="a" wire:navigate>Vocational & Technical</x-filament::dropdown.list.item>
                                </x-filament::dropdown.list>
                            </x-filament::dropdown>
                        </x-filament::dropdown.list.item>

                        {{-- Other Items --}}
                        {{-- <x-filament::dropdown.list.item :href="route('academic-calendar')" tag="a" wire:navigate>
                            Academic Calendar
                        </x-filament::dropdown.list.item> --}}
                        <x-filament::dropdown.list.item :href="route('result-checker')" tag="a" wire:navigate>
                            Result Checker
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown.list>
            </x-filament::dropdown>

            {{-- Responsive Admissions Dropdown --}}
            <x-filament::dropdown placement="top-start">
                <x-slot name="trigger">
                    <x-responsive-nav-link  :active="request()->routeIs('admissions')">
                        {{ __('Admissions') }} <x-filament::icon icon="heroicon-m-chevron-down" class="w-5 h-5 inline-block" />
                    </x-responsive-nav-link>
                </x-slot>
                <x-filament::dropdown.list>
                    <x-filament::dropdown.list.item :href="route('nce-full-time')" tag="a" wire:navigate>
                        NCE - Full Time
                    </x-filament::dropdown.list.item>
                </x-filament::dropdown.list>
            </x-filament::dropdown>

            <x-responsive-nav-link :href="route('news.index')" :active="request()->routeIs('news.index')" wire:navigate>
                {{ __('Campus News') }}
            </x-responsive-nav-link>

            <x-responsive-nav-link :href="route('contact')" :active="request()->routeIs('contact')" wire:navigate>
                {{ __('Contact us') }}
            </x-responsive-nav-link>

            <div class="border-t border-gray-200 my-2"></div>
            {{-- Login/Portal Link --}}
            @auth
                <x-responsive-nav-link :href="config('custom.portal_url')">
                    {{ __('Portal') }}
                </x-responsive-nav-link>
            @else
                <x-responsive-nav-link :href="config('custom.portal_url')">
                    {{ __('Login') }}
                </x-responsive-nav-link>
            @endauth
        </div>
    </div>
</nav>
