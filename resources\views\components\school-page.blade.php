@props(['title', 'philosophy', 'goals', 'departments'])

<x-layouts.app>
    <x-slot name="title">{{ __($title) }}</x-slot>

    <section class="bg-white py-16">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 sm:px-8">
            <h1 class="text-4xl font-bold text-center text-[#96281B] mb-12">{{ $title }}</h1>

            {{-- Philosophy --}}
            @if (!empty($philosophy))
                <div class="mb-12">
                    <h2 class="text-2xl font-semibold text-[#96281B] mb-4">Philosophy</h2>
                    @foreach ($philosophy as $para)
                        <p class="text-gray-700 leading-relaxed {{ !$loop->first ? 'mt-4' : '' }}">
                            {{ $para }}
                        </p>
                    @endforeach
                </div>
            @endif

            {{-- Goals --}}
            @if (!empty($goals))
                <div class="mb-12">
                    <h2 class="text-2xl font-semibold text-[#96281B] mb-4">Goals</h2>
                    <p class="text-gray-700 leading-relaxed pb-2">The NCE programme in the {{ strtolower($title) }} is designed to:</p>
                    <ul class="list-disc list-outside text-gray-700 space-y-2">
                        @foreach ($goals as $goal)
                            <li>{{ $goal }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            {{-- Departments --}}
            @if (!empty($departments))
                <div>
                    <h2 class="text-2xl font-semibold text-[#96281B] mb-4">Departments</h2>
                    <p class="text-gray-700 mb-6">The following are the list of departments in {{ strtolower($title) }}.</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        @foreach ($departments as $dept)
                            <a href="{{ $dept['route'] }}" class="block bg-gray-100 p-6 rounded-sm shadow hover:bg-gray-200 transition">
                                <h3 class="text-xl font-semibold text-[#96281B]">{{ $dept['name'] }}</h3>
                            </a>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </section>
</x-layouts.app>
