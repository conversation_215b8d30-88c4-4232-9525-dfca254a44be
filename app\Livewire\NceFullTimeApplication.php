<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Invoice;
use Livewire\Component;
use Filament\Forms\Form;
use App\Enums\FeeType;
use Livewire\Attributes\On;
use App\Enums\InvoiceStatus;
use Filament\Actions\Action;
use App\Settings\PortalSettings;
use App\Services\PaystackService;
use Illuminate\Support\HtmlString;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Contracts\HasForms;
use App\Services\CodeGenerationService;
use App\Filament\Components\StudentForm;
use Filament\Notifications\Notification;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Actions\Concerns\InteractsWithActions;

class NceFullTimeApplication extends Component implements HasForms, HasActions
{
    use InteractsWithActions;
    use InteractsWithForms;

    public ?string $activeSchoolSessionName;
    public ?array $formData = [];
    public int $portalFee;


    public function mount(): void
    {
        $this->form->fill();
        $this->activeSchoolSessionName = activeSchoolSession()?->name;
        $this->portalFee = app(PortalSettings::class)->portal_fee;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                StudentForm::schema(),
            )
            ->statePath('formData')
            ->model(User::class);
    }

    public function submitAction(): Void
    {
        $this->form->validate();
        $this->mountAction('confirmPaymentAction');
    }

    public function confirmPaymentAction(): Action
    {
        return Action::make('confirmPayment')
            ->requiresConfirmation()
            ->modalHeading('Confirm Payment and Application Submission?')
            ->modalDescription(new HtmlString("
                    You are about to pay a non-refundable portal fee of <b>₦" . number_format($this->portalFee) . "</b> and submit your NCE Full-Time application.<br>
                    Do you want to proceed?
                "))
            ->modalSubmitActionLabel('Proceed')
            ->mountUsing(fn() => $this->form->validate())
            ->action(fn() => $this->initiatePayment());
    }

    public function initiatePayment()
    {
        try {
            $applicationData = $this->form->getState();

            $item = [
                'item' => "Portal fee",
                'amount' => $this->portalFee,
            ];

            $invoice = Invoice::create([
                // 'user_id' => null, 
                // 'payable_id' => null,
                // 'payable_type' => null,
                'number' => app(CodeGenerationService::class)->generateInvoiceNumber(),
                'reference' => app(CodeGenerationService::class)->generateReference(),
                'description' => [$item],
                'total_amount' => $this->portalFee,
                'invoice_status' => InvoiceStatus::INITIATED,
                'fee_type' => FeeType::PORTAL,
                'metadata' => [
                    'applicationData' => $applicationData,
                ],

            ]);

            $response = (app(PaystackService::class))->initializePayment($applicationData['email'], $this->portalFee, $invoice->reference);
            $responseData = $response->json();

            if ($response->successful() && $responseData && $responseData['status']) {

                $this->dispatch('paystack-popup', accessCode: $responseData['data']['access_code'], invoiceId: $invoice->id);
            } else {
                $errorMessage = $responseData['message'] ?? 'Payment initialization failed with Paystack.';
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Payment Initiation Failed')
                ->body('Unable to process payment. Please try again. If the issue persists, contact the school admin.')
                ->persistent()
                ->send();

            Log::error('Paystack Payment Initiation Failed', [
                'error' => $e->getMessage(),
                'response' => isset($response) ? $response : 'No response received',
                'formData' => $applicationData
            ]);
        }
    }

    #[On('payment-success')]
    public function paymentSuccess($invoiceId)
    {
        $invoice = Invoice::withTrashed()->find($invoiceId);

        if ($invoice) {
            $invoice->update(['payment_success' => true]);

            if ($invoice->trashed()) {
                $invoice->restore();
            }
        }

        session()->flash('paid', true);

        return redirect()->route('thank-you');
    }

    #[On('payment-canceled')]
    public function paymentCanceled($invoiceId)
    {
        $invoice = Invoice::find($invoiceId);

        if ($invoice) {
            $invoice->delete();
        }

        Notification::make()
            ->danger()
            ->title('Payment Canceled')
            ->body('Payment must be completed to submit your application. Please try again or contact the school admin if you encounter any issues.')
            ->send();
    }

    public function render(): View
    {
        return view('livewire.pages.nce-full-time-application');
    }
}
