@import '../../../../vendor/filament/filament/resources/css/theme.css';

@config 'tailwind.config.js';

.fi-ta-table > thead > tr { 
  @apply divide-x divide-gray-200 dark:divide-white/5;
}

.fi-ta-row {
  @apply divide-x divide-gray-200 dark:divide-white/5;
} 

.fi-icon-btn {
    margin-right: 8px; 
  }

/* Specific component overrides */
.fi-btn,
.fi-tabs-item,
.fi-fo-tabs,
.fi-theme-switcher-btn,
.tippy-box,
.fi-dropdown-trigger,
.fi-ta-ctn,
.fi-input,
.fi-select,
.fi-ta-table,
.fi-card,
.fi-modal,
.fi-dropdown-panel,
.fi-input-wrp,
.fi-badge,
.fi-tabs,
.fi-pagination-item,
.fi-dropdown-list-item,
.fi-sidebar-item-button,
.fi-sidebar-group,
.fi-section,
.fi-avatar,
.fi-icon-btn,
.fi-checkbox-input,
.fi-modal-window,
.choices__list--dropdown,
.fi-fo-repeater-item,
.fi-fo-field-wrp,
.filepond--root,
.fi-fieldset,
.fi-tenant-menu-trigger,
.fi-fo-markdown-editor,
.fi-dropdown-list-item-image,
.fi-pagination-items,
.fi-fo-wizard,
.fi-fo-wizard-header-step-icon-ctn,
.fi-topbar-item-button,
.fi-simple-main,
.fi-wi-stats-overview-stat,
.fi-no-notification {
    border-radius: 2px !important;
}
