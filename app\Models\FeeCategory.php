<?php

namespace App\Models;

use App\Enums\FeeType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FeeCategory extends Model
{
    use HasFactory;

    public function getProtectedRelations(): array
    {
        return ['fees'];
    }

    protected $casts = [
        'description' => 'array',
        'fee_type' => FeeType::class,
    ];

    /**
     * Get all of the fees for the FeeCategory
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function fees(): HasMany
    {
        return $this->hasMany(Fee::class);
    }
}
