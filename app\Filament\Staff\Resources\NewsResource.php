<?php

namespace App\Filament\Staff\Resources;

use App\Enums\Role;
use Filament\Forms;
use App\Models\News;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Faker\Provider\ar_EG\Text;
use Filament\Support\Markdown;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Checkbox;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\ImageColumn;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ToggleColumn;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\MarkdownEditor;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Staff\Resources\NewsResource\Pages;
use App\Filament\Staff\Resources\NewsResource\RelationManagers;

class NewsResource extends Resource
{
    protected static ?string $model = News::class;

    protected static ?string $navigationGroup = 'Website';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationBadgeTooltip = 'Total number of news';
    protected static bool $isScopedToTenant = false;

    public static function canAccess(): bool
    {
        return management_staff_access();
    }
    public static function canCreate(): bool
    {
        return main_staff_access();
    }
    public static function canEdit($record): bool
    {
        return main_staff_access();
    }
    public static function canDelete($record): bool
    {
        return main_staff_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('title')
                    ->required()
                    ->maxLength(100)
                    ->label('Title')
                    ->placeholder('Excellence in Islamic Education: Basic School Updates')
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Set $set, ?string $old, ?string $state) {
                        if (empty($old)) {
                            $set('slug', Str::slug($state) . '-' . Str::random(5));
                        }
                    }),
                TextInput::make('slug')
                    ->required()
                    ->readOnly(),
                FileUpload::make('image')
                    ->columnSpan(2)
                    ->required()
                    ->label('Feature image')
                    ->directory('news')
                    ->image()
                    // ->imageResizeMode('cover')
                    // ->imageResizeTargetWidth(1080)
                    // ->imageResizeTargetHeight(700)
                    ->minSize(5)
                    ->maxSize(200)
                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Image size must be between 5kb-200kb.')
                    ->uploadingMessage('Uploading photo...'),
                DatePicker::make('date')
                    ->required()
                    ->placeholder('Pick a date')
                    ->native(false)
                    ->closeOnDateSelection(),
                Checkbox::make('is_published')
                    ->inline(false)
                    ->label('Publish')
                    ->helperText('Check to publish this news.'),
                MarkdownEditor::make('content')
                    ->disableToolbarButtons([
                        // 'attachFiles',
                        'table',
                    ])
                    ->fileAttachmentsDirectory('galleries')
                    ->required()
                    ->rules([
                        fn() => function ($attribute, $value, $fail) {
                            if (strlen(strip_tags($value)) > 5000) {
                                $fail("The content field must not exceed 5000 characters.");
                            }
                        },
                    ])
                    ->label('Content')
                    ->placeholder('News content goes here...')
                    ->columnSpan(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            // ->recordUrl(null)
            // this disable clickable row for simple resources instead
            ->recordAction(null)
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('No News Yet')
            ->emptyStateDescription('Once you create your first news, it will appear here.')
            ->description('The list of news published on the school website')
            ->columns([
                TextColumn::make('#')
                    ->width('2rem')
                    ->rowIndex(),
                ImageColumn::make('image')
                    ->width('3rem'),
                TextColumn::make('title')
                    ->width('2rem')
                    ->words(5)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        $wordCount = str_word_count($state);

                        if ($wordCount <= 5) {
                            return null;
                        }

                        return $state;
                    })
                    ->label('Title'),
                TextColumn::make('created_at')
                    ->label('Date')
                    ->date()
                    ->sinceTooltip()
                    ->sortable(),
                ToggleColumn::make('is_published')
                    ->label('Published')
            ])
            ->filters([
                //
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('News Updated')
                                ->body('The news has been updated successfully.');
                        }),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('News Deleted')
                                ->body('The news has been deleted.');
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageNews::route('/'),
        ];
    }
}
