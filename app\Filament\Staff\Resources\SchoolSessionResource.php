<?php

namespace App\Filament\Staff\Resources;


use Filament\Tables;
use App\Models\Semester;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\SchoolSession;
use Illuminate\Support\Carbon;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Repeater;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\ToggleColumn;
use App\Filament\Staff\Resources\SchoolSessionResource\Pages;

class SchoolSessionResource extends Resource
{
    protected static ?string $modelLabel = 'session';
    protected static ?string $model = SchoolSession::class;
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of sessions';

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canEdit($record): bool
    {
        return main_staff_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return main_staff_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->placeholder('2023/2024')
                    ->label('Session')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(9)
                    ->rule('regex:/^\d{4}\/\d{4}$/')  // Regex pattern to enforce the format YYYY/YYYY                   
                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Enter the session in the format YYYY/YYYY, e.g., 2023/2024.'),
                Checkbox::make('is_active')
                    ->inline(false)
                    ->label('Active')
                    ->helperText('Check to make this session active')
                    ->columnSpan(2),

                Repeater::make('semesterSchedules')
                    ->relationship()
                    ->schema([
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->native(false)
                            ->options(function () {
                                return Semester::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                            ->distinct(),
                        DatePicker::make('semester_start')
                            ->required()
                            ->label('Resumption date')
                            ->placeholder('Select a date')
                            ->native(false)
                            ->closeOnDateSelection(),
                        DatePicker::make('semester_end')
                            ->required()
                            ->label('Closing date')
                            ->placeholder('Select a date')
                            ->native(false)
                            ->closeOnDateSelection(),
                    ])
                    ->columnSpanFull()
                    ->columns(4)
                    ->minItems(fn() => Semester::count())
                    ->addActionLabel('Add semester'),

            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('name')
            ->emptyStateHeading('No Sessions Yet')
            ->emptyStateDescription('Once you create your first session, it will appear here.')
            ->description('The list of sessions created for efficient academic management.')

            ->columns([
                TextColumn::make('#')
                    ->width('2rem')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->width('2rem'),
                ToggleColumn::make('is_active')
                    ->label('Active')
                    ->disabled(fn($record) => $record->is_active || ! main_staff_access())
                    ->afterStateUpdated(function ($record, $state) {
                        if ($state) {
                            $record::where('id', '!=', $record->id)
                                ->update(['is_active' => false]);
                            Notification::make()
                                ->success()
                                ->title('Session Updated')
                                ->body('Active session was updated successfully.')
                                ->send();
                        }
                    }),
                TextColumn::make('semesters.name')
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->label('Semester'),
                ColumnGroup::make('Semester dates', [
                    TextColumn::make('semesterSchedules.semester_start')
                        ->listWithLineBreaks()
                        ->bulleted()
                        ->label('Resumption')
                        ->formatStateUsing(fn($state) => Carbon::parse($state)->format('d M, Y')),
                    TextColumn::make('semesterSchedules.semester_end')
                        ->listWithLineBreaks()
                        ->bulleted()
                        ->label('Closing')
                        ->formatStateUsing(fn($state) => Carbon::parse($state)->format('d M, Y')),
                ])->alignment(Alignment::Center)
            ])
            ->filters([
                //
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Semester Updated')
                                ->body('The Semester has been updated successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Session?')
                        ->modalDescription('Are you sure you want to delete this session?')
                        ->modalSubmitActionLabel('Yes, delete it')
                        ->modalCancelActionLabel('No, keep it')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['registrations'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this session because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Session Deleted')
                                ->body('The session has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Session actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSchoolSessions::route('/'),
        ];
    }
}
