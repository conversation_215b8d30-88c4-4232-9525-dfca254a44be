<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class PaystackService
{
    public function initializePayment(string $email, int|float $amount, string $reference): Response
    {
        $secretKey = config('custom.secret_key');
        $initializeUrl = config('custom.api_url') . '/transaction/initialize';
        $amountInKobo = MoneyService::toKobo($amount);

        if ($amountInKobo <= 0) {
            Log::error('Amount must be greater than zero.', [
                'email' => $email,
                'amount' => $amount,
                'reference' => $reference,
            ]);

            throw new \InvalidArgumentException('Amount must be greater than zero.');
        }

        $paymentData = [
            'email' => $email,
            'amount' => $amountInKobo,
            'reference' => $reference,
            'currency' => 'NGN',
        ];
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $secretKey,
            'Content-Type' => 'application/json',
        ])->post($initializeUrl, $paymentData);

        return $response;
    }

    public function verifyPaystackSignature(Request $request)
    {
        // Only handle POST requests with the Paystack signature header
        $paystackSignature = $request->header('X-Paystack-Signature');

        if (!$paystackSignature || strtoupper($request->method()) !== 'POST') {
            abort(403, 'Unauthorized');
        }

        // Retrieve the request's body and verify the signature
        $payload = $request->getContent();

        $PaystackSecretKey = config('custom.secret_key');

        if ($paystackSignature !== hash_hmac('sha512', $payload, $PaystackSecretKey)) {
            abort(403, 'Invalid signature');
        }

        return $payload;
    }
}
